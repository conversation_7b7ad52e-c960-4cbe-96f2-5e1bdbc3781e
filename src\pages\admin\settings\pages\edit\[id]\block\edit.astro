---
import AdminLayout from '@layouts/AdminLayout.astro';
import AdvancedEditorJS from '../../../../../../../settings/components/editor/AdvancedEditorJS.astro';
import CompactGridManager from '../../../../../../../settings/components/grid/CompactGridManager.astro';
import { loadPageSettings } from '../../../../../../../settings/utils/settingsLoader.js';

const { id } = Astro.params;
const blockId = new URL(Astro.request.url).searchParams.get('blockId');
const errorMessage = new URL(Astro.request.url).searchParams.get('error');

// Загружаем данные страницы и блока
let block = null;
let page = null;
if (id && blockId) {
  try {
    const settings = await loadPageSettings();
    page = settings.pages?.find(p => p.id === id);
    if (page) {
      block = page.blocks?.find(b => b.id === blockId);
    }
  } catch (error) {
    console.error('Ошибка загрузки блока:', error);
  }
}

// Значения по умолчанию
block = block || {
  id: blockId,
  type: 'text',
  order: 1,
  enabled: true,
  content: { ru: '', en: '' }
};

// Парсим JSON данные для редактора
let editorData = null;
if (block.content?.ru) {
  try {
    editorData = JSON.parse(block.content.ru);
  } catch (error) {
    console.error('Ошибка парсинга данных редактора:', error);
    editorData = null;
  }
}

// Настройки Grid-системы
const gridSystemSettings = block.displaySettings?.gridSystem || {
  enabled: false,
  mode: 'beginner',
  beginnerSettings: {
    columns: 2,
    gap: { value: 1, unit: 'rem' },
    itemAlignment: 'stretch',
    contentAlignment: 'start'
  },
  proSettings: {
    gridTemplateColumns: 'repeat(2, 1fr)',
    gap: { value: 1, unit: 'rem' },
    alignItems: 'stretch',
    justifyItems: 'stretch',
    alignContent: 'start',
    justifyContent: 'start'
  },
  items: []
};
---

<AdminLayout title={`Редактировать блок: ${blockId} (страница: ${id})`}>
  <div class="max-w-7xl w-full mx-auto bg-white rounded-lg shadow p-8 mt-8">
    <h1 class="text-2xl font-bold text-gray-900 mb-2">Редактировать блок</h1>
    <p class="text-gray-600 mb-6">Измените параметры блока и контент. После сохранения изменения будут применены.</p>

    {errorMessage && (
      <div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800">Ошибка при сохранении</h3>
            <div class="mt-2 text-sm text-red-700">
              <p>{decodeURIComponent(errorMessage)}</p>
            </div>
          </div>
        </div>
      </div>
    )}
    <form action="/api/settings/save-block" method="POST" class="space-y-6" onsubmit="return handleFormSubmit()">
      <input type="hidden" name="id" value={id} />
      <input type="hidden" name="blockId" value={blockId} />

      <!-- Скрытые поля для Grid-системы (будут заполнены JavaScript) -->
      <input type="hidden" name="gridEnabled" id="grid-enabled-hidden" />
      <input type="hidden" name="gridMode" id="grid-mode-hidden" />
      <input type="hidden" name="gridItemsData" id="grid-items-hidden" />

      <!-- Скрытые поля для настроек новичка -->
      <input type="hidden" name="beginnerColumns" id="beginner-columns-hidden" />
      <input type="hidden" name="beginnerGapValue" id="beginner-gap-value-hidden" />
      <input type="hidden" name="beginnerGapUnit" id="beginner-gap-unit-hidden" />
      <input type="hidden" name="beginnerItemAlignment" id="beginner-item-alignment-hidden" />
      <input type="hidden" name="beginnerContentAlignment" id="beginner-content-alignment-hidden" />

      <!-- Скрытые поля для настроек профи -->
      <input type="hidden" name="proGridTemplateColumns" id="pro-grid-template-columns-hidden" />
      <input type="hidden" name="proGridTemplateRows" id="pro-grid-template-rows-hidden" />
      <input type="hidden" name="proGridTemplateAreas" id="pro-grid-template-areas-hidden" />
      <input type="hidden" name="proGapValue" id="pro-gap-value-hidden" />
      <input type="hidden" name="proGapUnit" id="pro-gap-unit-hidden" />
      <input type="hidden" name="proAlignItems" id="pro-align-items-hidden" />
      <input type="hidden" name="proJustifyItems" id="pro-justify-items-hidden" />
      <input type="hidden" name="proAlignContent" id="pro-align-content-hidden" />
      <input type="hidden" name="proJustifyContent" id="pro-justify-content-hidden" />
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label class="block mb-1 text-sm font-medium text-gray-700">Название блока:</label>
          <input name="blockName" value={block.name || ''} placeholder="Введите название блока..." required class="w-full border rounded px-3 py-2 focus:ring-2 focus:ring-blue-500" />
        </div>
        <div>
          <label class="block mb-1 text-sm font-medium text-gray-700">Тип блока:</label>
          <select name="type" id="block-type" class="w-full border rounded px-3 py-2 focus:ring-2 focus:ring-blue-500" onchange="updateEditorTools()">
            <option value="text" selected={block.type === 'text'}>Текстовый</option>
            <option value="hero" selected={block.type === 'hero'}>Герой</option>
            <option value="features" selected={block.type === 'features'}>Особенности</option>
            <option value="gallery" selected={block.type === 'gallery'}>Галерея</option>
            <option value="contacts" selected={block.type === 'contacts'}>Контакты</option>
            <option value="map" selected={block.type === 'map'}>Карта</option>
            <option value="video" selected={block.type === 'video'}>Видео</option>
            <option value="forms" selected={block.type === 'forms'}>Формы</option>
            <option value="custom" selected={block.type === 'custom'}>Custom (все инструменты)</option>
          </select>
        </div>
        <div>
          <label class="block mb-1 text-sm font-medium text-gray-700">Порядок:</label>
          <input name="order" type="number" value={block.order || 1} min="1" required class="w-full border rounded px-3 py-2 focus:ring-2 focus:ring-blue-500" />
        </div>
        <div class="flex items-center">
          <input type="checkbox" name="enabled" checked={block.enabled !== false} class="mr-2 rounded border-gray-300 focus:ring-blue-500" />
          <label class="text-sm text-gray-700">Включён</label>
        </div>
        <div>
          <label class="block mb-1 text-sm font-medium text-gray-700">Язык:</label>
          <select name="lang" class="w-full border rounded px-3 py-2 focus:ring-2 focus:ring-blue-500">
            <option value="ru">Русский</option>
            <option value="en">English</option>
          </select>
        </div>
      </div>

      <!-- Настройки отображения блока -->
      <fieldset class="border border-gray-200 rounded-lg p-4">
        <legend class="text-base font-semibold text-gray-800 px-2">Настройки отображения</legend>

        <!-- Максимальная ширина -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div class="md:col-span-2">
            <label class="block mb-1 text-sm font-medium text-gray-700">Максимальная ширина блока:</label>
            <input
              name="displaySettings.maxWidth.value"
              type="number"
              value={block.displaySettings?.maxWidth?.value || 100}
              min="1"
              max="100"
              class="w-full border rounded px-3 py-2 focus:ring-2 focus:ring-blue-500"
              placeholder="100"
            />
          </div>
          <div>
            <label class="block mb-1 text-sm font-medium text-gray-700">Единица:</label>
            <select name="displaySettings.maxWidth.unit" class="w-full border rounded px-3 py-2 focus:ring-2 focus:ring-blue-500">
              <option value="%" selected={!block.displaySettings?.maxWidth?.unit || block.displaySettings?.maxWidth?.unit === '%'}>%</option>
              <option value="px" selected={block.displaySettings?.maxWidth?.unit === 'px'}>px</option>
            </select>
          </div>
        </div>

        <!-- Выравнивание -->
        <div class="mb-4">
          <label class="block mb-1 text-sm font-medium text-gray-700">Выравнивание контента:</label>
          <select name="displaySettings.alignment" class="w-full border rounded px-3 py-2 focus:ring-2 focus:ring-blue-500">
            <option value="left" selected={!block.displaySettings?.alignment || block.displaySettings?.alignment === 'left'}>Слева</option>
            <option value="center" selected={block.displaySettings?.alignment === 'center'}>По центру</option>
            <option value="right" selected={block.displaySettings?.alignment === 'right'}>Справа</option>
          </select>
        </div>

        <!-- Внутренние отступы (padding) -->
        <div class="mb-4">
          <label class="block mb-2 text-sm font-medium text-gray-700">Внутренние отступы (padding):</label>
          <div class="grid grid-cols-2 md:grid-cols-5 gap-2">
            <div>
              <label class="block text-xs text-gray-600 mb-1">Сверху:</label>
              <input
                name="displaySettings.padding.top"
                type="number"
                value={block.displaySettings?.padding?.top || 0}
                class="w-full border rounded px-2 py-1 text-sm focus:ring-2 focus:ring-blue-500"
                placeholder="0"
              />
            </div>
            <div>
              <label class="block text-xs text-gray-600 mb-1">Справа:</label>
              <input
                name="displaySettings.padding.right"
                type="number"
                value={block.displaySettings?.padding?.right || 0}
                class="w-full border rounded px-2 py-1 text-sm focus:ring-2 focus:ring-blue-500"
                placeholder="0"
              />
            </div>
            <div>
              <label class="block text-xs text-gray-600 mb-1">Снизу:</label>
              <input
                name="displaySettings.padding.bottom"
                type="number"
                value={block.displaySettings?.padding?.bottom || 0}
                class="w-full border rounded px-2 py-1 text-sm focus:ring-2 focus:ring-blue-500"
                placeholder="0"
              />
            </div>
            <div>
              <label class="block text-xs text-gray-600 mb-1">Слева:</label>
              <input
                name="displaySettings.padding.left"
                type="number"
                value={block.displaySettings?.padding?.left || 0}
                class="w-full border rounded px-2 py-1 text-sm focus:ring-2 focus:ring-blue-500"
                placeholder="0"
              />
            </div>
            <div>
              <label class="block text-xs text-gray-600 mb-1">Единица:</label>
              <select name="displaySettings.padding.unit" class="w-full border rounded px-2 py-1 text-sm focus:ring-2 focus:ring-blue-500">
                <option value="px" selected={!block.displaySettings?.padding?.unit || block.displaySettings?.padding?.unit === 'px'}>px</option>
                <option value="%" selected={block.displaySettings?.padding?.unit === '%'}>%</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Внешние отступы (margin) -->
        <div class="mb-4">
          <label class="block mb-2 text-sm font-medium text-gray-700">Внешние отступы (margin):</label>
          <div class="grid grid-cols-2 md:grid-cols-5 gap-2">
            <div>
              <label class="block text-xs text-gray-600 mb-1">Сверху:</label>
              <input
                name="displaySettings.margin.top"
                type="number"
                value={block.displaySettings?.margin?.top || 0}
                class="w-full border rounded px-2 py-1 text-sm focus:ring-2 focus:ring-blue-500"
                placeholder="0"
              />
            </div>
            <div>
              <label class="block text-xs text-gray-600 mb-1">Справа:</label>
              <input
                name="displaySettings.margin.right"
                type="number"
                value={block.displaySettings?.margin?.right || 0}
                class="w-full border rounded px-2 py-1 text-sm focus:ring-2 focus:ring-blue-500"
                placeholder="0"
              />
            </div>
            <div>
              <label class="block text-xs text-gray-600 mb-1">Снизу:</label>
              <input
                name="displaySettings.margin.bottom"
                type="number"
                value={block.displaySettings?.margin?.bottom || 0}
                class="w-full border rounded px-2 py-1 text-sm focus:ring-2 focus:ring-blue-500"
                placeholder="0"
              />
            </div>
            <div>
              <label class="block text-xs text-gray-600 mb-1">Слева:</label>
              <input
                name="displaySettings.margin.left"
                type="number"
                value={block.displaySettings?.margin?.left || 0}
                class="w-full border rounded px-2 py-1 text-sm focus:ring-2 focus:ring-blue-500"
                placeholder="0"
              />
            </div>
            <div>
              <label class="block text-xs text-gray-600 mb-1">Единица:</label>
              <select name="displaySettings.margin.unit" class="w-full border rounded px-2 py-1 text-sm focus:ring-2 focus:ring-blue-500">
                <option value="px" selected={!block.displaySettings?.margin?.unit || block.displaySettings?.margin?.unit === 'px'}>px</option>
                <option value="%" selected={block.displaySettings?.margin?.unit === '%'}>%</option>
              </select>
            </div>
          </div>
        </div>

      </fieldset>

      <!-- Новая Grid-система -->
      <fieldset class="border border-gray-200 rounded-lg p-4">
        <legend class="text-base font-semibold text-gray-800 px-2">Grid-система (новая)</legend>
        <p class="text-sm text-gray-600 mb-4">Расширенная система компоновки элементов с поддержкой режимов новичка и профи</p>

        <div id="grid-system-container">
          <CompactGridManager
            settings={gridSystemSettings}
            blockId={blockId || 'unknown'}
          />
        </div>
      </fieldset>

      <div>
        <label class="block mb-1 text-sm font-medium text-gray-700">Контент (Editor.js):</label>
        <div id="editor-container">
          <AdvancedEditorJS
            id="content"
            placeholder="Редактируйте контент блока..."
            minHeight={400}
            data={editorData}
            blockType={block.type}
          />
        </div>


      </div>
      <div class="flex gap-4 mt-8">
        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-6 py-2 rounded shadow transition">Сохранить</button>
        <a href={`/admin/settings/pages/edit/${id}`} class="bg-gray-200 hover:bg-gray-300 text-gray-700 px-6 py-2 rounded">Отмена</a>
      </div>
    </form>
  </div>
  <script is:inline>
    // Функция для обновления инструментов редактора при изменении типа блока
    function updateEditorTools() {
      const blockTypeSelect = document.getElementById('block-type');
      if (blockTypeSelect) {
        const newBlockType = blockTypeSelect.value;
        console.log(`🔄 Выбран тип блока: ${newBlockType}`);

        // Информация о доступных инструментах для каждого типа
        const toolsInfo = {
          text: ['Заголовки', 'Параграфы', 'Списки', 'Цитаты', 'Код', 'Разделители', 'Таблицы', 'Ссылки', 'HTML'],
          hero: ['Большие заголовки', 'Параграфы', 'Изображения', 'Ссылки', 'HTML'],
          features: ['Заголовки', 'Параграфы', 'Списки', 'Изображения', 'HTML'],
          gallery: ['Заголовки', 'Параграфы', 'Изображения', 'Разделители', 'HTML'],
          contacts: ['Заголовки', 'Параграфы', 'Списки', 'Ссылки', 'HTML'],
          map: ['Заголовки', 'Параграфы', 'Встраивание карт', 'Кнопки', 'HTML'],
          video: ['Заголовки', 'Параграфы', 'Встраивание видео', 'Кнопки', 'HTML'],
          forms: ['Заголовки', 'Параграфы', 'Списки', 'Кнопки', 'HTML'],
          custom: ['Заголовки', 'Параграфы', 'Списки', 'Цитаты', 'Код', 'Таблицы', 'Изображения', 'Ссылки', 'Встраивание', 'Кнопки', 'HTML', 'Разделители']
        };

        const tools = toolsInfo[newBlockType] || toolsInfo.text;
        console.log(`🛠️ Доступные инструменты: ${tools.join(', ')}`);

        // Показываем уведомление пользователю
        const notification = document.createElement('div');
        notification.className = 'fixed top-4 right-4 bg-blue-500 text-white px-4 py-2 rounded shadow-lg z-50';
        notification.textContent = `Выбран тип "${newBlockType}". Доступные инструменты: ${tools.join(', ')}`;
        document.body.appendChild(notification);

        setTimeout(() => {
          notification.remove();
        }, 5000);
      }
    }

    // Валидация настроек отображения
    function validateDisplaySettings() {
      const errors = [];

      // Валидация максимальной ширины
      const maxWidthElement = document.querySelector('input[name="displaySettings.maxWidth.value"]');
      if (maxWidthElement && maxWidthElement.value) {
        const maxWidthValue = parseFloat(maxWidthElement.value);
        if (!isNaN(maxWidthValue) && (maxWidthValue < 1 || maxWidthValue > 100)) {
          errors.push('Максимальная ширина должна быть от 1 до 100');
        }
      }



      // Валидация отступов (проверяем что значения не слишком большие)
      const paddingInputs = ['top', 'right', 'bottom', 'left'];
      paddingInputs.forEach(side => {
        const element = document.querySelector(`input[name="displaySettings.padding.${side}"]`);
        if (element && element.value) {
          const value = parseFloat(element.value);
          if (!isNaN(value) && Math.abs(value) > 1000) {
            errors.push(`Внутренний отступ ${side} не должен превышать 1000px`);
          }
        }
      });

      const marginInputs = ['top', 'right', 'bottom', 'left'];
      marginInputs.forEach(side => {
        const element = document.querySelector(`input[name="displaySettings.margin.${side}"]`);
        if (element && element.value) {
          const value = parseFloat(element.value);
          if (!isNaN(value) && Math.abs(value) > 1000) {
            errors.push(`Внешний отступ ${side} не должен превышать 1000px`);
          }
        }
      });

      return errors;
    }

    // Функция для сбора данных Grid-системы из CompactGridManager
    function collectGridSystemData() {
      console.log('🔍 collectGridSystemData вызвана');

      try {
        // Находим CompactGridManager на странице
        const gridManager = document.querySelector('.compact-grid-manager');
        if (!gridManager) {
          console.log('⚠️ CompactGridManager не найден');
          return;
        }

        const blockId = gridManager.dataset.blockId;
        console.log(`🔍 Собираем данные из CompactGridManager для блока ${blockId}`);

        // Собираем данные напрямую из DOM элементов (упрощенная версия без профилей)
        const gridEnabled = gridManager.querySelector('input[name="gridEnabled"]')?.checked || false;

        console.log('🔍 Основные настройки:', { gridEnabled });

        // Заполняем скрытые поля
        const gridEnabledField = document.getElementById('grid-enabled-hidden');
        const gridModeField = document.getElementById('grid-mode-hidden');
        const gridItemsField = document.getElementById('grid-items-hidden');

        if (gridEnabledField) gridEnabledField.value = gridEnabled ? 'on' : '';
        if (gridModeField) gridModeField.value = 'simple'; // Простой режим без профилей
        if (gridItemsField) gridItemsField.value = JSON.stringify([]);

        // Собираем настройки Grid (единый интерфейс)
        const gridSettings = {
          columns: Number(gridManager.querySelector('select[name="gridColumns"]')?.value) || 2,
          gapValue: Number(gridManager.querySelector('input[name="gridGapValue"]')?.value) || 1,
          gapUnit: gridManager.querySelector('select[name="gridGapUnit"]')?.value || 'rem',
          itemAlignment: gridManager.querySelector('select[name="gridItemAlignment"]')?.value || 'stretch',
          contentAlignment: gridManager.querySelector('select[name="gridContentAlignment"]')?.value || 'start'
        };

        console.log('🔍 Настройки Grid:', gridSettings);

        const fields = {
          'beginner-columns-hidden': gridSettings.columns,
          'beginner-gap-value-hidden': gridSettings.gapValue,
          'beginner-gap-unit-hidden': gridSettings.gapUnit,
          'beginner-item-alignment-hidden': gridSettings.itemAlignment,
          'beginner-content-alignment-hidden': gridSettings.contentAlignment
        };

        Object.entries(fields).forEach(([id, value]) => {
          const field = document.getElementById(id);
          console.log(`🔍 Поле ${id}:`, { field: !!field, value });
          if (field && value !== undefined) {
            field.value = value;
            console.log(`✅ Установлено значение ${value} для поля ${id}`);
          }
        });

        console.log('✅ Grid-система данные собраны из CompactGridManager:', { gridEnabled, gridSettings });

      } catch (error) {
        console.error('❌ Ошибка при сборе данных Grid-системы:', error);

        // В случае ошибки устанавливаем безопасные значения по умолчанию
        const gridEnabledField = document.getElementById('grid-enabled-hidden');
        const gridModeField = document.getElementById('grid-mode-hidden');
        const gridItemsField = document.getElementById('grid-items-hidden');

        if (gridEnabledField) gridEnabledField.value = '';
        if (gridModeField) gridModeField.value = 'beginner';
        if (gridItemsField) gridItemsField.value = '[]';
      }
    }

    // Обработчик отправки формы
    async function handleFormSubmit() {
      try {
        console.log('🚀 Отправка формы начата');

        // Принудительно собираем данные Grid-системы перед отправкой
        try {
          collectGridSystemData();
          console.log('✅ Данные Grid-системы собраны успешно');
        } catch (error) {
          console.error('❌ Ошибка сбора данных Grid-системы:', error);
        }

        // Показываем все скрытые поля для отладки
        const hiddenFields = document.querySelectorAll('input[type="hidden"]');
        console.log('🔍 Скрытые поля формы:');
        hiddenFields.forEach(field => {
          if (field.name.includes('beginner') || field.name.includes('grid')) {
            console.log(`  ${field.name}: "${field.value}"`);
          }
        });

        // Валидируем настройки отображения
        const validationErrors = validateDisplaySettings();
        if (validationErrors.length > 0) {
          alert('Ошибки валидации:\n' + validationErrors.join('\n'));
          return false;
        }

        console.log('📝 Начинаем сохранение данных из EditorJS...');

        // Получаем экземпляр редактора с несколькими попытками
        let editorInstance = window['editor-content_instance'];
        const contentInput = document.getElementById('content-data');
        const editorReady = window['editor-content_ready'];

        console.log('🔍 Проверяем экземпляр редактора:', editorInstance);
        console.log('🔍 Проверяем готовность редактора:', editorReady);
        console.log('🔍 Проверяем поле ввода:', contentInput);

        // Если редактор не найден или не готов, ждем немного и пробуем снова
        if (!editorInstance || !editorReady) {
          console.log('⏳ Редактор не готов, ждем инициализации...');

          // Ждем до 2 секунд для инициализации редактора
          let attempts = 0;
          const maxAttempts = 10;

          while ((!editorInstance || !window['editor-content_ready']) && attempts < maxAttempts) {
            await new Promise(resolve => setTimeout(resolve, 200));
            editorInstance = window['editor-content_instance'];
            attempts++;
            console.log(`⏳ Попытка ${attempts}/${maxAttempts}...`);
          }

          if (!editorInstance) {
            console.warn('⚠️ Редактор не инициализирован после ожидания');
          }
        }

        if (editorInstance && contentInput) {
          console.log('📝 Сохраняем данные из EditorJS...');

          try {
            // Проверяем, что редактор готов
            if (typeof editorInstance.save !== 'function') {
              throw new Error('Редактор не готов к сохранению');
            }

            // Получаем данные из редактора
            const outputData = await editorInstance.save();
            contentInput.value = JSON.stringify(outputData);

            // Собираем данные Grid-системы
            collectGridSystemData();

            console.log('✅ Данные сохранены:', outputData);
            console.log('✅ Значение поля:', contentInput.value);
            return true;
          } catch (saveError) {
            console.error('❌ Ошибка при сохранении данных редактора:', saveError);

            // Fallback: используем последние сохраненные данные
            if (window.editorjsLastData) {
              console.log('🔄 Используем последние сохраненные данные');
              contentInput.value = window.editorjsLastData;
              collectGridSystemData();
              return true;
            }

            throw saveError;
          }
        } else {
          console.error('❌ Редактор или поле не найдены');

          // Попробуем использовать глобальную переменную как fallback
          if (contentInput && window.editorjsLastData) {
            console.log('🔄 Используем fallback данные:', window.editorjsLastData);
            contentInput.value = window.editorjsLastData;
            collectGridSystemData();
            return true;
          }

          alert('Ошибка: не удалось получить данные из редактора. Попробуйте еще раз через несколько секунд.');
          return false;
        }
      } catch (error) {
        console.error('❌ Ошибка при сохранении:', error);
        alert('Ошибка при сохранении данных: ' + error.message);
        return false;
      }
    }

    // Инициализация Grid-системы
    document.addEventListener('DOMContentLoaded', function() {
      // Загружаем JavaScript для Grid-системы
      const scripts = [
        '/src/settings/utils/editorjs-grid-parser.js',
        '/src/settings/components/grid/grid-system.js'
      ];

      let loadedScripts = 0;

      scripts.forEach(src => {
        const script = document.createElement('script');
        script.src = src;
        script.onload = function() {
          loadedScripts++;
          if (loadedScripts === scripts.length) {
            initializeGridSystem();
          }
        };
        document.head.appendChild(script);
      });

      function initializeGridSystem() {
        // Инициализируем Grid-систему для текущего блока
        if (window.GridSystemManager) {
          const blockId = `${blockId || "unknown"}`;
          window.gridSystemManager = new window.GridSystemManager(blockId);
          console.log('Grid-система инициализирована для блока:', blockId);

          // Инициализация CompactGridManager
          console.log('🚀 Инициализация CompactGridManager...');

          // Находим все компактные Grid-менеджеры на странице
          const gridManagers = document.querySelectorAll('.compact-grid-manager');
          console.log(`🔍 Найдено ${gridManagers.length} компактных менеджеров`);

          gridManagers.forEach((manager, index) => {
            const blockId = manager.dataset.blockId;
            console.log(`🔧 Инициализация менеджера ${index + 1} для блока ${blockId}`);

            // Обработчик изменения настроек
            const settingsInputs = manager.querySelectorAll('select, input[type="number"], input[type="text"]');
            console.log('🔍 Найдено элементов для отслеживания:', settingsInputs.length);

            settingsInputs.forEach((input, index) => {
              console.log(`🔍 Элемент ${index + 1}:`, input.name, input.tagName, input.type);
              input.addEventListener('change', function() {
                console.log('🔄 Настройка изменена:', input.name, '=', input.value);
                // Обновляем данные Grid-системы
                collectGridSystemData();
              });
            });

            // Также отслеживаем изменения чекбокса Grid
            const gridToggle = manager.querySelector('input[name="gridEnabled"]');
            if (gridToggle) {
              gridToggle.addEventListener('change', function() {
                console.log('🔄 Grid включен/выключен:', gridToggle.checked);
                // Показываем/скрываем настройки
                const settingsContainer = manager.querySelector('.grid-settings-container');
                if (settingsContainer) {
                  settingsContainer.style.display = gridToggle.checked ? 'block' : 'none';
                }
                // Обновляем данные Grid-системы
                collectGridSystemData();
              });
            }
          });


        }
      }


    });
  </script>
</AdminLayout>
