import edjsHTML from 'editorjs-html';

/**
 * Парсер для преобразования Editor.js JSON в HTML
 */
class EditorJSParser {
  constructor() {
    // Создаем экземпляр парсера с базовыми настройками
    this.parser = edjsHTML();
  }

  /**
   * Обрабатывает кастомные блоки, которые не поддерживаются стандартной библиотекой
   */
  processCustomBlocks(editorData, currentHtml) {
    if (!editorData.blocks || !Array.isArray(editorData.blocks)) {
      return currentHtml;
    }

    // Список кастомных блоков, которые мы обрабатываем отдельно
    const customBlockTypes = ['button', 'raw', 'table', 'delimiter', 'linkTool', 'code', 'image'];

    // Фильтруем только кастомные блоки
    const customBlocks = editorData.blocks.filter(block =>
      customBlockTypes.includes(block.type)
    );

    // Если нет кастомных блоков, возвращаем исходный HTML
    if (customBlocks.length === 0) {
      return currentHtml;
    }

    // Обрабатываем кастомные блоки
    let customHtml = '';
    customBlocks.forEach(block => {
      const blockHtml = this.parseCustomBlock(block);
      if (blockHtml) {
        customHtml += blockHtml;
      }
    });

    // Возвращаем комбинированный HTML
    return currentHtml + customHtml;
  }

  /**
   * Парсит отдельный кастомный блок
   */
  parseCustomBlock(block) {
    if (!block || !block.type || !block.data) {
      return '';
    }

    switch (block.type) {
      case 'button':
        return this.parseButtonBlock(block.data);
      case 'raw':
        return this.parseRawBlock(block.data);
      case 'table':
        return this.parseTableBlock(block.data);
      case 'delimiter':
        return this.parseDelimiterBlock(block.data);
      case 'linkTool':
        return this.parseLinkToolBlock(block.data);
      case 'code':
        return this.parseCodeBlock(block.data);
      case 'image':
        return this.parseImageBlock(block.data);
      default:
        return '';
    }
  }

  /**
   * Парсит блок кнопки
   */
  parseButtonBlock(data) {
    if (!data || !data.link || !data.text) {
      return '';
    }
    return `<div class="mb-6">
      <a href="${data.link}"
         class="inline-block bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200 shadow-md hover:shadow-lg">
        ${data.text}
      </a>
    </div>`;
  }

  /**
   * Парсит блок сырого HTML
   */
  parseRawBlock(data) {
    if (!data || !data.html) {
      return '';
    }
    return `<div class="mb-6">${data.html}</div>`;
  }

  /**
   * Парсит блок таблицы
   */
  parseTableBlock(data) {
    if (!data || !data.content || !Array.isArray(data.content)) {
      return '';
    }

    const rows = data.content.map((row, index) => {
      if (!Array.isArray(row)) return '';

      const cells = row.map(cell => {
        const tag = index === 0 ? 'th' : 'td';
        const classes = index === 0 ? 'px-4 py-2 bg-gray-100 font-semibold text-left border-b' : 'px-4 py-2 border-b';
        return `<${tag} class="${classes}">${cell || ''}</${tag}>`;
      }).join('');
      return `<tr>${cells}</tr>`;
    }).join('');

    return `<div class="mb-6 overflow-x-auto">
      <table class="min-w-full border border-gray-300 rounded-lg">
        <tbody>${rows}</tbody>
      </table>
    </div>`;
  }

  /**
   * Парсит блок разделителя
   */
  parseDelimiterBlock(data) {
    return `<hr class="my-8 border-t border-gray-300">`;
  }

  /**
   * Парсит блок ссылки
   */
  parseLinkToolBlock(data) {
    if (!data || !data.link) {
      return '';
    }

    const image = data.meta?.image?.url ?
      `<img src="${data.meta.image.url}" alt="${data.meta.title || ''}" class="w-full h-32 object-cover rounded-t-lg">` : '';

    return `<div class="mb-6 border border-gray-300 rounded-lg overflow-hidden hover:shadow-md transition-shadow">
      <a href="${data.link}" target="_blank" rel="noopener noreferrer" class="block">
        ${image}
        <div class="p-4">
          <h3 class="font-semibold text-blue-600 hover:text-blue-800 mb-2">${data.meta?.title || data.link}</h3>
          ${data.meta?.description ? `<p class="text-gray-600 text-sm">${data.meta.description}</p>` : ''}
        </div>
      </a>
    </div>`;
  }

  /**
   * Парсит блок кода (переопределяем стандартный парсер)
   */
  parseCodeBlock(data) {
    if (!data || !data.code || data.code.trim() === '') {
      return ''; // Возвращаем пустую строку для пустого кода
    }
    return `<pre class="bg-gray-900 text-green-400 p-4 rounded-lg mb-6 overflow-x-auto">
      <code>${this.escapeHtmlStatic(data.code)}</code>
    </pre>`;
  }

  /**
   * Парсит блок изображения (переопределяем стандартный парсер)
   */
  parseImageBlock(data) {
    if (!data || !data.file || !data.file.url) {
      return ''; // Возвращаем пустую строку для пустого изображения
    }

    const caption = data.caption ? `<figcaption class="text-sm text-gray-600 text-center mt-2">${data.caption}</figcaption>` : '';
    const borderClass = data.withBorder ? 'border border-gray-300' : '';
    const backgroundClass = data.withBackground ? 'bg-gray-100 p-4' : '';
    const stretchedClass = data.stretched ? 'w-full' : 'max-w-full';

    return `<figure class="mb-6 ${backgroundClass}">
      <img src="${data.file.url}" alt="${data.caption || ''}"
           class="${stretchedClass} h-auto ${borderClass} rounded-lg shadow-md">
      ${caption}
    </figure>`;
  }

  /**
   * Применяет стили Tailwind CSS к HTML (только для стандартных элементов)
   */
  applyTailwindStyles(html) {
    return html
      // Заголовки (только если у них еще нет классов)
      .replace(/<h1(?![^>]*class)>/g, '<h1 class="text-4xl font-bold mb-4 text-gray-900">')
      .replace(/<h2(?![^>]*class)>/g, '<h2 class="text-3xl font-bold mb-4 text-gray-900">')
      .replace(/<h3(?![^>]*class)>/g, '<h3 class="text-2xl font-bold mb-4 text-gray-900">')
      .replace(/<h4(?![^>]*class)>/g, '<h4 class="text-xl font-bold mb-4 text-gray-900">')
      .replace(/<h5(?![^>]*class)>/g, '<h5 class="text-lg font-bold mb-4 text-gray-900">')
      .replace(/<h6(?![^>]*class)>/g, '<h6 class="text-base font-bold mb-4 text-gray-900">')

      // Параграфы (только если у них еще нет классов)
      .replace(/<p(?![^>]*class)>/g, '<p class="mb-4 text-gray-700 leading-relaxed">')

      // Списки (только если у них еще нет классов)
      .replace(/<ul(?![^>]*class)>/g, '<ul class="list-disc list-inside mb-6 space-y-2">')
      .replace(/<ol(?![^>]*class)>/g, '<ol class="list-decimal list-inside mb-6 space-y-2">')
      .replace(/<li(?![^>]*class)>/g, '<li class="mb-2 text-gray-700">')

      // Изображения (только если у них еще нет классов)
      .replace(/<img([^>]*?)(?![^>]*class)([^>]*)>/g, '<img$1 class="max-w-full h-auto rounded-lg shadow-md mb-6"$2>')

      // Цитаты (только если у них еще нет классов)
      .replace(/<blockquote(?![^>]*class)>/g, '<blockquote class="border-l-4 border-blue-500 pl-6 py-4 mb-6 bg-gray-50 italic">')

      // Ссылки в тексте (только простые ссылки без классов)
      .replace(/<a([^>]*?)(?![^>]*class)([^>]*)>/g, '<a$1 class="text-blue-600 hover:text-blue-800 underline"$2>');
  }

  /**
   * Получает размер заголовка для Tailwind CSS
   */
  getHeaderSize(level) {
    return this.getHeaderSizeStatic(level);
  }

  /**
   * Статический метод для получения размера заголовка
   */
  getHeaderSizeStatic(level) {
    const sizes = {
      1: '4xl',
      2: '3xl',
      3: '2xl',
      4: 'xl',
      5: 'lg',
      6: 'base'
    };
    return sizes[level] || 'xl';
  }

  /**
   * Статический метод для экранирования HTML символов
   */
  escapeHtmlStatic(text) {
    if (typeof text !== 'string') return '';

    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');
  }

  /**
   * Экранирует HTML символы
   */
  escapeHtml(text) {
    return this.escapeHtmlStatic(text);
  }

  /**
   * Статический метод для экранирования HTML символов
   */
  escapeHtmlStatic(text) {
    if (typeof text !== 'string') return '';

    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');
  }

  /**
   * Парсит JSON строку от Editor.js в HTML с поддержкой Grid-системы
   * @param {string} jsonString - JSON строка от Editor.js
   * @param {boolean} gridEnabled - Включена ли Grid-система
   * @param {Object} gridSettings - Настройки Grid-системы
   * @returns {string} HTML строка
   */
  parseToHTML(jsonString, gridEnabled = false, gridSettings = undefined) {
    try {
      // Проверяем, является ли входная строка JSON
      if (!jsonString || typeof jsonString !== 'string') {
        return '';
      }

      // Пытаемся распарсить JSON
      const editorData = JSON.parse(jsonString);

      // Проверяем структуру данных Editor.js
      if (!editorData.blocks || !Array.isArray(editorData.blocks)) {
        return '';
      }

      // Фильтруем блоки для стандартного парсера (исключаем кастомные)
      const customBlockTypes = ['button', 'raw', 'table', 'delimiter', 'linkTool', 'code', 'image'];
      const standardBlocks = editorData.blocks.filter(block =>
        !customBlockTypes.includes(block.type)
      );

      // Создаем данные только со стандартными блоками
      const standardData = {
        ...editorData,
        blocks: standardBlocks
      };

      // Преобразуем стандартные блоки в HTML
      const htmlResult = this.parser.parse(standardData);

      // Проверяем тип результата
      let htmlString = '';
      if (typeof htmlResult === 'string') {
        htmlString = htmlResult;
      } else if (Array.isArray(htmlResult)) {
        htmlString = htmlResult.join('');
      } else {
        console.error('Парсер вернул неожиданный тип:', typeof htmlResult, htmlResult);
        return '';
      }

      // Обрабатываем кастомные блоки отдельно
      const customHtml = this.processCustomBlocks(editorData, '');

      // Объединяем HTML
      let combinedHtml = htmlString + customHtml;

      // Если Grid-система включена, оборачиваем в Grid-контейнер
      if (gridEnabled) {
        combinedHtml = this.wrapForGrid(combinedHtml, gridSettings);
      }

      // Применяем стили Tailwind CSS
      return this.applyTailwindStyles(combinedHtml);

    } catch (error) {
      console.error('Ошибка парсинга Editor.js JSON:', error);
      // Возвращаем исходную строку как fallback
      return jsonString;
    }
  }

  /**
   * Оборачивает HTML в Grid-контейнер для Grid-системы
   * @param {string} html - HTML строка
   * @param {Object} gridSettings - Настройки Grid-системы
   * @returns {string} HTML с Grid-обёрткой
   */
  wrapForGrid(html, gridSettings = undefined) {
    if (!html || html.trim() === '') return html;

    // Генерируем inline стили для Grid
    let gridStyles = 'display: grid;';

    if (gridSettings && gridSettings.enabled) {
      if (gridSettings.mode === 'beginner') {
        // Используем настройки из beginnerSettings или значения по умолчанию
        const beginnerSettings = gridSettings.beginnerSettings || {};
        const columns = beginnerSettings.columns || 2; // По умолчанию 2 колонки
        const gap = beginnerSettings.gap || { value: 1, unit: 'rem' }; // По умолчанию 1rem
        const itemAlignment = beginnerSettings.itemAlignment || 'stretch';
        const contentAlignment = beginnerSettings.contentAlignment || 'start';

        gridStyles += ` grid-template-columns: repeat(${columns}, 1fr);`;
        gridStyles += ` gap: ${gap.value}${gap.unit};`;
        gridStyles += ` align-items: ${itemAlignment};`;
        gridStyles += ` justify-content: ${contentAlignment};`;

      } else if (gridSettings.mode === 'pro' && gridSettings.proSettings) {
        const {
          gridTemplateColumns,
          gridTemplateRows,
          gridTemplateAreas,
          gap,
          alignItems,
          justifyItems,
          alignContent,
          justifyContent
        } = gridSettings.proSettings;

        if (gridTemplateColumns) {
          gridStyles += ` grid-template-columns: ${gridTemplateColumns};`;
        }

        if (gridTemplateRows) {
          gridStyles += ` grid-template-rows: ${gridTemplateRows};`;
        }

        if (gridTemplateAreas) {
          gridStyles += ` grid-template-areas: ${gridTemplateAreas};`;
        }

        if (gap) {
          gridStyles += ` gap: ${gap.value}${gap.unit};`;
        }

        if (alignItems) {
          gridStyles += ` align-items: ${alignItems};`;
        }

        if (justifyItems) {
          gridStyles += ` justify-items: ${justifyItems};`;
        }

        if (alignContent) {
          gridStyles += ` align-content: ${alignContent};`;
        }

        if (justifyContent) {
          gridStyles += ` justify-content: ${justifyContent};`;
        }
      }
    }

    // Оборачиваем весь контент в Grid-контейнер с inline стилями
    return `<div class="editorjs-grid-container" style="${gridStyles}">${html}</div>`;
  }

  /**
   * Парсит контент блока с учетом языка
   * @param {Object} block - Блок контента
   * @param {string} lang - Язык (ru, en)
   * @param {boolean} gridEnabled - Включена ли Grid-система
   * @returns {string} HTML строка
   */
  parseBlockContent(block, lang = 'ru', gridEnabled = false, gridSettings = undefined) {
    if (!block || !block.content || !block.content[lang]) {
      return '';
    }

    const content = block.content[lang];

    // Если контент - это строка JSON от Editor.js
    if (typeof content === 'string') {
      const html = this.parseToHTML(content, gridEnabled, gridSettings);

      // Новая Grid-система обрабатывается отдельными компонентами

      return html;
    }

    // Если контент - это объект с title и body (старый формат)
    if (typeof content === 'object' && content.title && content.body) {
      return `
        <h2 class="text-3xl font-bold mb-4 text-gray-900">${content.title}</h2>
        <div class="text-gray-700">${content.body}</div>
      `;
    }

    return '';
  }

  // Функция wrapForGrid удалена - используется новая Grid-система
}

// Создаем глобальный экземпляр парсера
const editorJSParser = new EditorJSParser();

export default editorJSParser;
