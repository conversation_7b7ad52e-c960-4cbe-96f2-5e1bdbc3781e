---
import type { GridSystemSettings } from '../../types';

export interface Props {
  settings: GridSystemSettings;
  blockId: string;
}

const { settings, blockId } = Astro.props;

// Значения по умолчанию (упрощенная версия без профилей)
const currentSettings = {
  enabled: settings?.enabled || false,
  columns: settings?.columns || settings?.beginnerSettings?.columns || 2,
  gap: settings?.gap || settings?.beginnerSettings?.gap || { value: 1, unit: 'rem' },
  itemAlignment: settings?.itemAlignment || settings?.beginnerSettings?.itemAlignment || 'stretch',
  contentAlignment: settings?.contentAlignment || settings?.beginnerSettings?.contentAlignment || 'start',
  previewActive: settings?.previewActive || false,
  items: settings?.items || []
};
---

<div class="compact-grid-manager" data-block-id={blockId}>

  <!-- Скрытое поле для хранения данных Grid-системы -->
  <input
    type="hidden"
    name="gridSystemData"
    value={JSON.stringify({
      enabled: currentSettings.enabled,
      columns: currentSettings.columns,
      gap: currentSettings.gap,
      itemAlignment: currentSettings.itemAlignment,
      contentAlignment: currentSettings.contentAlignment,
      previewActive: settings?.previewActive || false,
      items: currentSettings.items
    })}
  />

  <!-- Переключатель включения Grid -->
  <div class="grid-toggle mb-4">
    <label class="flex items-center space-x-3 cursor-pointer">
      <input
        type="checkbox"
        id={`grid-enabled-${blockId}`}
        name="gridEnabled"
        checked={currentSettings.enabled}
        class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
      />
      <span class="text-sm font-medium text-gray-900">Включить Grid-систему</span>
    </label>
  </div>

  <!-- Настройки Grid -->
  <div class="grid-settings-container" style={currentSettings.enabled ? '' : 'display: none;'}>

    <!-- Основные настройки Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">




      <!-- Левая панель - Настройки -->
      <div class="settings-panel">
        <h4 class="text-sm font-medium text-gray-900 mb-3">Настройки Grid</h4>

        <!-- Количество колонок -->
        <div class="setting-group mb-3">
          <label class="block text-xs font-medium text-gray-700 mb-1">Колонки:</label>
          <select
            name="beginnerColumns"
            class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
          >
            <option value="1" selected={currentSettings.columns === 1}>1 колонка</option>
            <option value="2" selected={currentSettings.columns === 2}>2 колонки</option>
            <option value="3" selected={currentSettings.columns === 3}>3 колонки</option>
            <option value="4" selected={currentSettings.columns === 4}>4 колонки</option>
            <option value="5" selected={currentSettings.columns === 5}>5 колонок</option>
            <option value="6" selected={currentSettings.columns === 6}>6 колонок</option>
          </select>
        </div>

        <!-- Отступы между элементами -->
        <div class="setting-group mb-3">
          <label class="block text-xs font-medium text-gray-700 mb-1">Отступы:</label>
          <div class="flex space-x-2">
            <input
              type="number"
              name="beginnerGapValue"
              value={currentSettings.gap.value}
              min="0"
              max="10"
              step="0.1"
              class="flex-1 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
            />
            <select
              name="beginnerGapUnit"
              class="px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
            >
              <option value="rem" selected={currentSettings.gap.unit === 'rem'}>rem</option>
              <option value="px" selected={currentSettings.gap.unit === 'px'}>px</option>
              <option value="em" selected={currentSettings.gap.unit === 'em'}>em</option>
              <option value="%" selected={currentSettings.gap.unit === '%'}>%</option>
            </select>
          </div>
        </div>

        <!-- Выравнивание элементов -->
        <div class="setting-group mb-3">
          <label class="block text-xs font-medium text-gray-700 mb-1">Выравнивание элементов:</label>
          <select
            name="beginnerItemAlignment"
            class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
          >
            <option value="stretch" selected={currentSettings.itemAlignment === 'stretch'}>Растянуть</option>
            <option value="start" selected={currentSettings.itemAlignment === 'start'}>Начало</option>
            <option value="center" selected={currentSettings.itemAlignment === 'center'}>Центр</option>
            <option value="end" selected={currentSettings.itemAlignment === 'end'}>Конец</option>
          </select>
        </div>

        <!-- Выравнивание содержимого -->
        <div class="setting-group mb-3">
          <label class="block text-xs font-medium text-gray-700 mb-1">Выравнивание содержимого:</label>
          <select
            name="beginnerContentAlignment"
            class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
          >
            <option value="start" selected={currentSettings.contentAlignment === 'start'}>Начало</option>
            <option value="center" selected={currentSettings.contentAlignment === 'center'}>Центр</option>
            <option value="end" selected={currentSettings.contentAlignment === 'end'}>Конец</option>
            <option value="space-between" selected={currentSettings.contentAlignment === 'space-between'}>Между</option>
            <option value="space-around" selected={currentSettings.contentAlignment === 'space-around'}>Вокруг</option>
            <option value="space-evenly" selected={currentSettings.contentAlignment === 'space-evenly'}>Равномерно</option>
          </select>
        </div>
      </div>

      <!-- Правая панель - Превью -->
        <div class="preview-panel">
          <h4 class="text-sm font-medium text-gray-900 mb-3">Превью</h4>
          <div class="preview-container bg-gray-50 border border-gray-200 rounded p-3 min-h-[200px]">
            <div id={`grid-preview-${blockId}`} class="grid-preview-content">
              {!currentSettings.previewActive ? (
                <div class="text-center text-gray-500 text-sm py-8">
                  <p>Создайте элементы в редакторе</p>
                  <p>и нажмите "Показать превью"</p>
                </div>
              ) : (
                <div class="text-center text-gray-500 text-sm py-8">
                  <p>Превью будет отображено здесь</p>
                </div>
              )}
            </div>

            <!-- Кнопка превью Grid -->
            <div class="mt-3 text-center" id={`grid-preview-button-container-${blockId}`}>
              <button
                type="button"
                id={`grid-preview-button-${blockId}`}
                class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
              >
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                Показать превью Grid
              </button>
              <p class="text-xs text-gray-500 mt-2">Создайте элементы в редакторе и нажмите кнопку для превью</p>
            </div>
          </div>
        </div>

      </div>
    </div>

  </div>

  <!-- Скрытые поля для передачи данных -->
  <input type="hidden" name="gridSystemData" id={`grid-system-data-${blockId}`} value={JSON.stringify(currentSettings)} />
</div>

<style>
  .compact-grid-manager {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    padding: 1rem;
  }

  .setting-group {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.25rem;
    padding: 0.75rem;
  }

  .preview-container {
    position: relative;
    overflow: hidden;
  }

  .grid-preview-content {
    display: grid;
    gap: 0.5rem;
    min-height: 150px;
  }

  /* Анимации для сворачивания/разворачивания */
  .grid-settings-container {
    transition: all 0.3s ease;
  }

  .grid-main-panel {
    transition: all 0.3s ease;
  }

  /* Стили для превью Grid */
  .grid-preview-content {
    min-height: 200px;
    display: grid;
    gap: 1rem;
    border: 2px dashed #e5e7eb;
    border-radius: 0.5rem;
    padding: 1rem;
    background: #f9fafb;
  }

  .grid-preview-item {
    background: #dbeafe;
    border: 2px dashed #3b82f6;
    border-radius: 0.375rem;
    padding: 0.75rem;
    font-size: 0.875rem;
    color: #1e40af;
    min-height: 60px;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .grid-preview-item {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    padding: 1rem;
    min-height: 100px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    font-size: 0.875rem;
    color: #374151;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  }

  .grid-preview-item h1, .grid-preview-item h2, .grid-preview-item h3,
  .grid-preview-item h4, .grid-preview-item h5, .grid-preview-item h6 {
    margin: 0 0 0.5rem 0;
    font-weight: 600;
  }

  .grid-preview-item p {
    margin: 0 0 0.5rem 0;
  }

  .grid-preview-item img {
    max-width: 100%;
    height: auto;
    border-radius: 0.25rem;
  }

  .grid-preview-item ul, .grid-preview-item ol {
    margin: 0;
    padding-left: 1.5rem;
  }

  /* Адаптивность */
  @media (max-width: 1024px) {
    .grid-main-panel .grid {
      grid-template-columns: 1fr;
    }
  }
</style>

<script>
  console.log('CompactGridManager: Скрипт загружен');

  document.addEventListener('DOMContentLoaded', function() {
    console.log('CompactGridManager: DOM загружен, инициализация...');

    // Находим все компактные Grid-менеджеры на странице
    const gridManagers = document.querySelectorAll('.compact-grid-manager');
    console.log(`CompactGridManager: Найдено ${gridManagers.length} менеджеров`);

    gridManagers.forEach((manager, index) => {
      const blockId = manager.dataset.blockId;
      console.log(`CompactGridManager: Инициализация менеджера ${index + 1} для блока ${blockId}`);

      const gridToggle = manager.querySelector('input[name="gridEnabled"]');
      const settingsContainer = manager.querySelector('.grid-settings-container');
      const modeRadios = manager.querySelectorAll('input[name^="gridMode"]');
      const beginnerSettings = manager.querySelector('.beginner-settings');
      const proSettings = manager.querySelector('.pro-settings');
      const mainPanel = manager.querySelector('.grid-main-panel');

      console.log('CompactGridManager: Элементы найдены:', {
        gridToggle: !!gridToggle,
        settingsContainer: !!settingsContainer,
        modeRadios: modeRadios.length,
        beginnerSettings: !!beginnerSettings,
        proSettings: !!proSettings,
        mainPanel: !!mainPanel
      });

      // Отладка начального состояния
      if (gridToggle) {
        console.log(`🔍 Начальное состояние Grid для блока ${blockId}:`, {
          checked: gridToggle.checked,
          id: gridToggle.id,
          name: gridToggle.name
        });
      }

      if (!gridToggle || !settingsContainer) {
        console.error('CompactGridManager: Не найдены обязательные элементы');
        return;
      }

      // Функция для показа/скрытия настроек
      function toggleGridSettings() {
        const isEnabled = gridToggle.checked;
        console.log(`🔄 CompactGridManager: toggleGridSettings вызвана для блока ${blockId}, isEnabled = ${isEnabled}`);
        console.log(`🔍 Стек вызова:`, new Error().stack);

        settingsContainer.style.display = isEnabled ? 'block' : 'none';
        console.log(`CompactGridManager: settingsContainer.style.display = ${settingsContainer.style.display}`);

        // Обновляем скрытые поля gridSystemData
        updateGridSystemData();

        // Уведомляем другие компоненты об изменении состояния Grid
        const event = new CustomEvent('gridToggled', {
          detail: { blockId, enabled: isEnabled }
        });
        document.dispatchEvent(event);

        console.log(`CompactGridManager: Grid ${isEnabled ? 'включен' : 'отключен'} для блока ${blockId}`);
      }



      // Функция для переключения режимов
      function toggleMode() {
        const selectedMode = manager.querySelector('input[name^="gridMode"]:checked')?.value;

        if (beginnerSettings && proSettings && mainPanel) {
          if (selectedMode === 'beginner') {
            beginnerSettings.style.display = 'block';
            proSettings.style.display = 'none';
            mainPanel.style.display = 'block';
          } else if (selectedMode === 'pro') {
            beginnerSettings.style.display = 'none';
            proSettings.style.display = 'block';
            mainPanel.style.display = 'block';
          } else {
            mainPanel.style.display = 'none';
          }
        }

        // Обновляем скрытые поля gridSystemData при изменении режима
        updateGridSystemData();

        console.log(`Режим Grid изменен на ${selectedMode} для блока ${blockId}`);
      }

      // Функция для настройки кнопки превью
      function setupGridPreviewButton() {
        const previewButton = document.getElementById(`grid-preview-button-${blockId}`);
        const previewContainer = document.getElementById(`grid-preview-${blockId}`);

        if (previewButton && previewContainer) {
          previewButton.addEventListener('click', async function() {
            try {
              // Показываем состояние загрузки
              previewButton.disabled = true;
              previewButton.innerHTML = `
                <svg class="animate-spin w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Загрузка...
              `;

              // Получаем данные из редактора
              const editorInstance = window.editorInstances?.content;
              if (!editorInstance) {
                throw new Error('Редактор не найден');
              }

              const editorData = await editorInstance.save();

              if (!editorData.blocks || editorData.blocks.length === 0) {
                throw new Error('В редакторе нет элементов для отображения в Grid');
              }

              // Передаем данные в Grid-систему для превью
              if (window.EditorJSGridParser) {
                const gridItems = window.EditorJSGridParser.parseBlocksForGrid(editorData.blocks);
                renderGridPreview(gridItems);
                console.log(`Grid превью обновлено с ${gridItems.length} элементами`);
              } else {
                throw new Error('EditorJSGridParser не загружен');
              }

              // Восстанавливаем кнопку
              previewButton.disabled = false;
              previewButton.innerHTML = `
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                Показать превью Grid
              `;

            } catch (error) {
              console.error('Ошибка при создании превью Grid:', error);

              // Показываем ошибку в превью
              previewContainer.innerHTML = `
                <div class="text-center text-red-500 text-sm py-8">
                  <p>Ошибка: ${error.message}</p>
                </div>
              `;

              // Восстанавливаем кнопку
              previewButton.disabled = false;
              previewButton.innerHTML = `
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                Показать превью Grid
              `;
            }
          });
        }
      }

      // Функция для рендеринга превью Grid
      function renderGridPreview(items) {
        const previewContainer = document.getElementById(`grid-preview-${blockId}`);
        if (!previewContainer) return;

        const enabled = gridToggle.checked;
        if (!enabled) {
          previewContainer.innerHTML = '<p class="text-gray-500 text-center py-8">Grid отключен</p>';
          return;
        }

        const mode = manager.querySelector('input[name^="gridMode"]:checked')?.value || 'beginner';

        if (mode === 'beginner') {
          renderBeginnerPreview(items, previewContainer);
        } else {
          renderProPreview(items, previewContainer);
        }
      }

      // Функция для рендеринга превью в режиме новичка
      function renderBeginnerPreview(items, container) {
        const columns = parseInt(manager.querySelector('select[name="beginnerColumns"]')?.value || '2');
        const gapValue = parseFloat(manager.querySelector('input[name="beginnerGapValue"]')?.value || '1');
        const gapUnit = manager.querySelector('select[name="beginnerGapUnit"]')?.value || 'rem';

        container.style.gridTemplateColumns = `repeat(${columns}, 1fr)`;
        container.style.gap = `${gapValue}${gapUnit}`;
        container.style.border = '2px dashed #e5e7eb';
        container.style.borderRadius = '0.375rem';
        container.style.padding = '1rem';

        if (items.length === 0) {
          container.innerHTML = '<div class="text-center text-gray-500 text-sm py-8">Нет элементов для отображения</div>';
          return;
        }

        container.innerHTML = items.map((item, index) => {
          let content = 'Пустой элемент';

          if (item.editorData) {
            if (item.type === 'paragraph' && item.editorData.text) {
              content = item.editorData.text.substring(0, 100) + (item.editorData.text.length > 100 ? '...' : '');
            } else if (item.type === 'header' && item.editorData.text) {
              content = `H${item.editorData.level || 1}: ${item.editorData.text.substring(0, 50)}`;
            } else if (item.type === 'image' && item.editorData.file) {
              content = `🖼️ Изображение: ${item.editorData.file.name || 'Без названия'}`;
            } else if (item.type === 'list' && item.editorData.items) {
              content = `📋 Список (${item.editorData.items.length} элементов)`;
            } else {
              content = `📄 ${item.type}`;
            }
          }

          return `
            <div class="grid-preview-item">
              <div class="text-xs text-gray-400 mb-1">Элемент ${index + 1} (${item.type})</div>
              <div class="text-sm">${content}</div>
            </div>
          `;
        }).join('');
      }

      // Функция для рендеринга превью в режиме профи
      function renderProPreview(items, container) {
        const gridTemplateColumns = manager.querySelector('input[name="proGridTemplateColumns"]')?.value || 'repeat(2, 1fr)';
        const gapValue = parseFloat(manager.querySelector('input[name="proGapValue"]')?.value || '1');
        const gapUnit = manager.querySelector('select[name="proGapUnit"]')?.value || 'rem';

        container.style.gridTemplateColumns = gridTemplateColumns;
        container.style.gap = `${gapValue}${gapUnit}`;
        container.style.border = '2px dashed #e5e7eb';
        container.style.borderRadius = '0.375rem';
        container.style.padding = '1rem';

        if (items.length === 0) {
          container.innerHTML = '<div class="text-center text-gray-500 text-sm py-8">Нет элементов для отображения</div>';
          return;
        }

        container.innerHTML = items.map((item, index) => {
          let content = 'Пустой элемент';

          if (item.editorData) {
            if (item.type === 'paragraph' && item.editorData.text) {
              content = item.editorData.text.substring(0, 100) + (item.editorData.text.length > 100 ? '...' : '');
            } else if (item.type === 'header' && item.editorData.text) {
              content = `H${item.editorData.level || 1}: ${item.editorData.text.substring(0, 50)}`;
            } else if (item.type === 'image' && item.editorData.file) {
              content = `🖼️ Изображение: ${item.editorData.file.name || 'Без названия'}`;
            } else if (item.type === 'list' && item.editorData.items) {
              content = `📋 Список (${item.editorData.items.length} элементов)`;
            } else {
              content = `📄 ${item.type}`;
            }
          }

          const gridColumnStyle = item.gridColumn ? `grid-column: ${item.gridColumn};` : '';
          const gridRowStyle = item.gridRow ? `grid-row: ${item.gridRow};` : '';
          const combinedStyle = gridColumnStyle + gridRowStyle;

          return `
            <div class="grid-preview-item" ${combinedStyle ? `style="${combinedStyle}"` : ''}>
              <div class="text-xs text-gray-400 mb-1">Элемент ${index + 1} (${item.type})</div>
              <div class="text-sm">${content}</div>
            </div>
          `;
        }).join('');
      }

      // Обработчики событий
      gridToggle.addEventListener('change', toggleGridSettings);
      modeRadios.forEach(radio => {
        radio.addEventListener('change', toggleMode);
      });

      // Обработчик изменения настроек
      console.log('🔍 Менеджер:', manager);
      console.log('🔍 ID менеджера:', manager.id);

      const settingsInputs = manager.querySelectorAll('select, input[type="number"], input[type="text"]');
      console.log('🔍 Найдено элементов для отслеживания:', settingsInputs.length);

      // Дополнительная проверка - ищем конкретно beginnerColumns
      const beginnerColumnsSelect = manager.querySelector('select[name="beginnerColumns"]');
      console.log('🔍 beginnerColumns найден:', beginnerColumnsSelect);

      settingsInputs.forEach((input, index) => {
        console.log(`🔍 Элемент ${index + 1}:`, input.name, input.tagName, input.type);
        input.addEventListener('change', function() {
          console.log('🔄 Настройка изменена:', input.name, '=', input.value);
          // Обновляем скрытое поле с данными Grid-системы
          updateGridSystemData();
        });
      });

      // Функция обновления скрытого поля с данными Grid-системы
      function updateGridSystemData() {
        const gridSystemDataField = manager.querySelector('input[name="gridSystemData"]');
        if (gridSystemDataField) {
          const enabled = manager.querySelector('input[name="gridEnabled"]')?.checked || false;

          const currentData = {
            enabled: enabled,
            columns: Number(manager.querySelector('select[name="beginnerColumns"]')?.value) || 2,
            gap: {
              value: Number(manager.querySelector('input[name="beginnerGapValue"]')?.value) || 1,
              unit: manager.querySelector('select[name="beginnerGapUnit"]')?.value || 'rem'
            },
            itemAlignment: manager.querySelector('select[name="beginnerItemAlignment"]')?.value || 'stretch',
            contentAlignment: manager.querySelector('select[name="beginnerContentAlignment"]')?.value || 'start',
            previewActive: false,
            items: []
          };

          gridSystemDataField.value = JSON.stringify(currentData);
          console.log('🔄 Обновлены данные Grid-системы:', currentData);
        }
      }

      // Инициализация кнопки превью
      setupGridPreviewButton();

      // Инициальная настройка
      toggleGridSettings();
      toggleMode();
      updateGridSystemData(); // Обновляем данные при инициализации

      // Создаем объект для совместимости с функцией collectGridSystemData
      if (!window.gridSystemManager) {
        window.gridSystemManager = {
          getSettings: function() {
            const gridEnabled = manager.querySelector('input[name="gridEnabled"]')?.checked || false;
            const gridSystemData = manager.querySelector('input[name="gridSystemData"]')?.value || '{}';

            console.log('🔍 CompactGridManager.getSettings():', {
              blockId,
              gridEnabled
            });

            let settings = {
              enabled: gridEnabled,
              items: []
            };

            // Парсим данные элементов
            try {
              const systemData = JSON.parse(gridSystemData);
              settings.items = systemData.items || [];
              settings.columns = systemData.columns || 2;
              settings.gap = systemData.gap || { value: 1, unit: 'rem' };
              settings.itemAlignment = systemData.itemAlignment || 'stretch';
              settings.contentAlignment = systemData.contentAlignment || 'start';
            } catch (e) {
              console.error('Ошибка парсинга данных Grid-системы:', e);
              // Fallback: собираем данные напрямую из полей формы
              const columnsSelect = manager.querySelector('select[name="beginnerColumns"]');
              const gapValueInput = manager.querySelector('input[name="beginnerGapValue"]');
              const gapUnitSelect = manager.querySelector('select[name="beginnerGapUnit"]');
              const itemAlignmentSelect = manager.querySelector('select[name="beginnerItemAlignment"]');
              const contentAlignmentSelect = manager.querySelector('select[name="beginnerContentAlignment"]');

              settings.columns = Number(columnsSelect?.value) || 2;
              settings.gap = {
                value: Number(gapValueInput?.value) || 1,
                unit: gapUnitSelect?.value || 'rem'
              };
              settings.itemAlignment = itemAlignmentSelect?.value || 'stretch';
              settings.contentAlignment = contentAlignmentSelect?.value || 'start';
            }

            console.log('🔍 Собраны настройки Grid:', settings);
            return settings;
          }
        };
      }
    });
  });
</script>
