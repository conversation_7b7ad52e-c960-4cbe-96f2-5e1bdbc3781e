---
import type { GridSystemSettings, GridBeginnerSettings, GridProSettings } from '../../types';
import GridVisualizer from './GridVisualizer.astro';

export interface Props {
  settings: GridSystemSettings;
  blockId: string;
  onUpdate?: (settings: GridSystemSettings) => void;
}

const { settings, blockId } = Astro.props;

// Значения по умолчанию для новичков
const defaultBeginnerSettings: GridBeginnerSettings = {
  columns: 2,
  rows: undefined,
  gap: { value: 1, unit: 'rem' },
  itemAlignment: 'stretch',
  contentAlignment: 'start'
};

// Значения по умолчанию для профи
const defaultProSettings: GridProSettings = {
  gridTemplateColumns: 'repeat(2, 1fr)',
  gridTemplateRows: undefined,
  gridTemplateAreas: undefined,
  gap: { value: 1, unit: 'rem' },
  alignItems: 'stretch',
  justifyItems: 'stretch',
  alignContent: 'start',
  justifyContent: 'start'
};

// Получаем текущие настройки или значения по умолчанию
const currentSettings = {
  enabled: settings?.enabled || false,
  mode: settings?.mode || 'beginner',
  beginnerSettings: settings?.beginnerSettings || defaultBeginnerSettings,
  proSettings: settings?.proSettings || defaultProSettings,
  items: settings?.items || []
};
---

<div class="grid-system-manager" data-block-id={blockId}>

  <!-- Скрытое поле для хранения данных Grid-системы -->
  <input
    type="hidden"
    name="gridSystemData"
    value={JSON.stringify({
      enabled: currentSettings.enabled,
      mode: currentSettings.mode,
      previewActive: settings?.previewActive || false,
      items: currentSettings.items
    })}
  />

  <!-- Переключатель включения/выключения Grid -->
  <div class="grid-toggle-section mb-6">
    <label class="flex items-center space-x-3">
      <input
        type="checkbox"
        id={`grid-enabled-${blockId}`}
        name="gridEnabled"
        checked={currentSettings.enabled}
        class="w-5 h-5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
      />
      <span class="text-lg font-medium text-gray-900">Включить Grid-систему</span>
    </label>
    <p class="text-sm text-gray-600 mt-2">
      Grid-система позволяет создавать сложные макеты с элементами, расположенными в сетке
    </p>
  </div>

  <!-- Настройки Grid (показываются только если включен) -->
  <div class="grid-settings" style={currentSettings.enabled ? '' : 'display: none;'}>
    
    <!-- Выбор режима -->
    <div class="mode-selector mb-6">
      <h3 class="text-lg font-medium text-gray-900 mb-3">Режим настройки</h3>
      <div class="flex space-x-4">
        <label class="flex items-center space-x-2">
          <input 
            type="radio" 
            name={`gridMode-${blockId}`}
            value="beginner"
            checked={currentSettings.mode === 'beginner'}
            class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500"
          />
          <span class="text-gray-900">Новичок</span>
        </label>
        <label class="flex items-center space-x-2">
          <input 
            type="radio" 
            name={`gridMode-${blockId}`}
            value="pro"
            checked={currentSettings.mode === 'pro'}
            class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500"
          />
          <span class="text-gray-900">Профи</span>
        </label>
      </div>
    </div>

    <!-- Настройки для новичков -->
    <div class="beginner-settings" style={currentSettings.mode === 'beginner' ? '' : 'display: none;'}>
      <h4 class="text-md font-medium text-gray-900 mb-4">Простые настройки</h4>
      
      <!-- Количество колонок -->
      <div class="setting-group mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-2">
          Количество колонок
        </label>
        <select
          name="beginnerColumns"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="1" selected={currentSettings.beginnerSettings.columns === 1}>1 колонка</option>
          <option value="2" selected={currentSettings.beginnerSettings.columns === 2}>2 колонки</option>
          <option value="3" selected={currentSettings.beginnerSettings.columns === 3}>3 колонки</option>
          <option value="4" selected={currentSettings.beginnerSettings.columns === 4}>4 колонки</option>
          <option value="5" selected={currentSettings.beginnerSettings.columns === 5}>5 колонок</option>
          <option value="6" selected={currentSettings.beginnerSettings.columns === 6}>6 колонок</option>
          <option value="7" selected={currentSettings.beginnerSettings.columns === 7}>7 колонок</option>
          <option value="8" selected={currentSettings.beginnerSettings.columns === 8}>8 колонок</option>
          <option value="9" selected={currentSettings.beginnerSettings.columns === 9}>9 колонок</option>
          <option value="10" selected={currentSettings.beginnerSettings.columns === 10}>10 колонок</option>
          <option value="11" selected={currentSettings.beginnerSettings.columns === 11}>11 колонок</option>
          <option value="12" selected={currentSettings.beginnerSettings.columns === 12}>12 колонок</option>
        </select>
      </div>

      <!-- Отступы между элементами -->
      <div class="setting-group mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-2">
          Отступы между элементами
        </label>
        <div class="flex space-x-2">
          <input 
            type="number"
            name="beginnerGapValue"
            value={currentSettings.beginnerSettings.gap.value}
            min="0"
            step="0.1"
            class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <select 
            name="beginnerGapUnit"
            class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="px" selected={currentSettings.beginnerSettings.gap.unit === 'px'}>px</option>
            <option value="rem" selected={currentSettings.beginnerSettings.gap.unit === 'rem'}>rem</option>
          </select>
        </div>
      </div>

      <!-- Выравнивание элементов -->
      <div class="setting-group mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-2">
          Выравнивание элементов
        </label>
        <select 
          name="beginnerItemAlignment"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="start" selected={currentSettings.beginnerSettings.itemAlignment === 'start'}>По верху</option>
          <option value="center" selected={currentSettings.beginnerSettings.itemAlignment === 'center'}>По центру</option>
          <option value="end" selected={currentSettings.beginnerSettings.itemAlignment === 'end'}>По низу</option>
          <option value="stretch" selected={currentSettings.beginnerSettings.itemAlignment === 'stretch'}>Растянуть</option>
        </select>
      </div>

      <!-- Выравнивание контента -->
      <div class="setting-group mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-2">
          Распределение контента
        </label>
        <select 
          name="beginnerContentAlignment"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="start" selected={currentSettings.beginnerSettings.contentAlignment === 'start'}>К началу</option>
          <option value="center" selected={currentSettings.beginnerSettings.contentAlignment === 'center'}>По центру</option>
          <option value="end" selected={currentSettings.beginnerSettings.contentAlignment === 'end'}>К концу</option>
          <option value="space-between" selected={currentSettings.beginnerSettings.contentAlignment === 'space-between'}>Между элементами</option>
          <option value="space-around" selected={currentSettings.beginnerSettings.contentAlignment === 'space-around'}>Вокруг элементов</option>
          <option value="space-evenly" selected={currentSettings.beginnerSettings.contentAlignment === 'space-evenly'}>Равномерно</option>
        </select>
      </div>
    </div>

    <!-- Настройки для профи -->
    <div class="pro-settings" style={currentSettings.mode === 'pro' ? '' : 'display: none;'}>
      <h4 class="text-md font-medium text-gray-900 mb-4">Продвинутые настройки</h4>
      
      <!-- Grid Template Columns -->
      <div class="setting-group mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-2">
          Grid Template Columns
        </label>
        <input 
          type="text"
          name="proGridTemplateColumns"
          value={currentSettings.proSettings.gridTemplateColumns}
          placeholder="repeat(3, 1fr) или 200px 1fr 100px"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
        <p class="text-xs text-gray-500 mt-1">Определяет размеры и количество колонок</p>
      </div>

      <!-- Grid Template Rows -->
      <div class="setting-group mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-2">
          Grid Template Rows (опционально)
        </label>
        <input 
          type="text"
          name="proGridTemplateRows"
          value={currentSettings.proSettings.gridTemplateRows || ''}
          placeholder="repeat(2, 100px) или auto 1fr"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
        <p class="text-xs text-gray-500 mt-1">Определяет размеры строк (если не указано - авто)</p>
      </div>

      <!-- Gap -->
      <div class="setting-group mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-2">
          Отступы между элементами
        </label>
        <div class="flex space-x-2">
          <input 
            type="number"
            name="proGapValue"
            value={currentSettings.proSettings.gap.value}
            min="0"
            step="0.1"
            class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <select 
            name="proGapUnit"
            class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="px" selected={currentSettings.proSettings.gap.unit === 'px'}>px</option>
            <option value="rem" selected={currentSettings.proSettings.gap.unit === 'rem'}>rem</option>
          </select>
        </div>
      </div>

      <!-- Align Items -->
      <div class="setting-group mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-2">
          Align Items (выравнивание по вертикали)
        </label>
        <select 
          name="proAlignItems"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="start" selected={currentSettings.proSettings.alignItems === 'start'}>Start</option>
          <option value="center" selected={currentSettings.proSettings.alignItems === 'center'}>Center</option>
          <option value="end" selected={currentSettings.proSettings.alignItems === 'end'}>End</option>
          <option value="stretch" selected={currentSettings.proSettings.alignItems === 'stretch'}>Stretch</option>
        </select>
      </div>

      <!-- Justify Items -->
      <div class="setting-group mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-2">
          Justify Items (выравнивание по горизонтали)
        </label>
        <select 
          name="proJustifyItems"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="start" selected={currentSettings.proSettings.justifyItems === 'start'}>Start</option>
          <option value="center" selected={currentSettings.proSettings.justifyItems === 'center'}>Center</option>
          <option value="end" selected={currentSettings.proSettings.justifyItems === 'end'}>End</option>
          <option value="stretch" selected={currentSettings.proSettings.justifyItems === 'stretch'}>Stretch</option>
        </select>
      </div>
    </div>

    <!-- Визуальная схема Grid -->
    <div class="grid-preview mb-6">
      <h4 class="text-md font-medium text-gray-900 mb-3">Предварительный просмотр</h4>
      <GridVisualizer
        mode={currentSettings.mode}
        beginnerSettings={currentSettings.beginnerSettings}
        proSettings={currentSettings.proSettings}
        items={currentSettings.items}
        blockId={blockId}
      />
    </div>

  </div>
</div>

<style>
  .grid-system-manager {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    padding: 1.5rem;
  }

  .setting-group {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    padding: 1rem;
  }

  .grid-preview-content {
    min-height: 200px;
    display: grid;
    gap: 1rem;
  }

  /* Стили для предварительного просмотра */
  .preview-item {
    background: #dbeafe;
    border: 2px dashed #3b82f6;
    border-radius: 0.375rem;
    padding: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    color: #1e40af;
    font-weight: 500;
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const gridManagers = document.querySelectorAll('.grid-system-manager');

    gridManagers.forEach(manager => {
      const blockId = manager.dataset.blockId;
      const gridToggle = manager.querySelector('input[name="gridEnabled"]');
      const settingsContainer = manager.querySelector('.grid-settings');

      if (gridToggle && settingsContainer) {
        // Функция для показа/скрытия настроек и генерации события
        function toggleGridSettings() {
          const isEnabled = gridToggle.checked;
          settingsContainer.style.display = isEnabled ? 'block' : 'none';

          // Генерируем событие для уведомления других компонентов
          const event = new CustomEvent('gridToggled', {
            detail: { blockId, enabled: isEnabled }
          });
          document.dispatchEvent(event);

          console.log(`GridSystemManager: Grid ${isEnabled ? 'включен' : 'отключен'} для блока ${blockId}`);
        }

        // Обработчик изменения состояния
        gridToggle.addEventListener('change', toggleGridSettings);

        // Инициальная настройка
        toggleGridSettings();
      }
    });
  });
</script>
