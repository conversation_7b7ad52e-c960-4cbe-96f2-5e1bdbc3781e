---
import AdminLayout from '@layouts/AdminLayout.astro';
import AdvancedEditorJS from '../../../../../../../settings/components/editor/AdvancedEditorJS.astro';
import CompactGridManager from '../../../../../../../settings/components/grid/CompactGridManager.astro';

const { id } = Astro.params;

// Генерируем уникальный ID для нового блока
const newBlockId = Math.random().toString(36).slice(2,10);
---

<AdminLayout title={`Добавить блок на страницу: ${id}`}>
  <div class="max-w-7xl w-full mx-auto bg-white rounded-lg shadow p-8 mt-8">
    <h1 class="text-2xl font-bold text-gray-900 mb-2">Добавить блок</h1>
    <p class="text-gray-600 mb-6">Заполните параметры блока и добавьте контент. После сохранения блок появится в списке на странице.</p>
    <form action="/api/settings/save-block" method="POST" class="space-y-6" onsubmit="return handleFormSubmit()">
      <input type="hidden" name="id" value={id} />

      <!-- Скрытые поля для Grid-системы (будут заполнены JavaScript) -->
      <input type="hidden" name="gridEnabled" id="grid-enabled-hidden" />
      <input type="hidden" name="gridMode" id="grid-mode-hidden" />
      <input type="hidden" name="gridItemsData" id="grid-items-hidden" />

      <!-- Скрытые поля для настроек новичка -->
      <input type="hidden" name="beginnerColumns" id="beginner-columns-hidden" />
      <input type="hidden" name="beginnerGapValue" id="beginner-gap-value-hidden" />
      <input type="hidden" name="beginnerGapUnit" id="beginner-gap-unit-hidden" />
      <input type="hidden" name="beginnerItemAlignment" id="beginner-item-alignment-hidden" />
      <input type="hidden" name="beginnerContentAlignment" id="beginner-content-alignment-hidden" />

      <!-- Скрытые поля для настроек профи -->
      <input type="hidden" name="proGridTemplateColumns" id="pro-grid-template-columns-hidden" />
      <input type="hidden" name="proGridTemplateRows" id="pro-grid-template-rows-hidden" />
      <input type="hidden" name="proGridTemplateAreas" id="pro-grid-template-areas-hidden" />
      <input type="hidden" name="proGapValue" id="pro-gap-value-hidden" />
      <input type="hidden" name="proGapUnit" id="pro-gap-unit-hidden" />
      <input type="hidden" name="proAlignItems" id="pro-align-items-hidden" />
      <input type="hidden" name="proJustifyItems" id="pro-justify-items-hidden" />
      <input type="hidden" name="proAlignContent" id="pro-align-content-hidden" />
      <input type="hidden" name="proJustifyContent" id="pro-justify-content-hidden" />
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label class="block mb-1 text-sm font-medium text-gray-700">Тип блока:</label>
          <select name="type" id="block-type" class="w-full border rounded px-3 py-2 focus:ring-2 focus:ring-blue-500" onchange="updateEditorTools()">
            <option value="text">Текстовый</option>
            <option value="hero">Герой</option>
            <option value="features">Особенности</option>
            <option value="gallery">Галерея</option>
            <option value="contacts">Контакты</option>
            <option value="map">Карта</option>
            <option value="video">Видео</option>
            <option value="forms">Формы</option>
            <option value="custom">Custom (все инструменты)</option>
          </select>
        </div>
        <div>
          <label class="block mb-1 text-sm font-medium text-gray-700">Название блока:</label>
          <input name="blockName" placeholder="Введите название блока..." required class="w-full border rounded px-3 py-2 focus:ring-2 focus:ring-blue-500" />
        </div>
        <div>
          <label class="block mb-1 text-sm font-medium text-gray-700">ID блока:</label>
          <input name="blockId" value={newBlockId} required class="w-full border rounded px-3 py-2 focus:ring-2 focus:ring-blue-500" />
        </div>
        <div>
          <label class="block mb-1 text-sm font-medium text-gray-700">Порядок:</label>
          <input name="order" type="number" value="" min="1" placeholder="Автоматически" class="w-full border rounded px-3 py-2 focus:ring-2 focus:ring-blue-500" />
          <small class="text-gray-500">Оставьте пустым для автоматического назначения</small>
        </div>
        <div class="flex items-center mt-6">
          <input type="checkbox" name="enabled" checked class="mr-2 rounded border-gray-300 focus:ring-blue-500" />
          <label class="text-sm text-gray-700">Включён</label>
        </div>
        <div>
          <label class="block mb-1 text-sm font-medium text-gray-700">Язык:</label>
          <select name="lang" class="w-full border rounded px-3 py-2 focus:ring-2 focus:ring-blue-500">
            <option value="ru">Русский</option>
            <option value="en">English</option>
          </select>
        </div>
      </div>

      <!-- Настройки отображения блока -->
      <fieldset class="border border-gray-200 rounded-lg p-4">
        <legend class="text-base font-semibold text-gray-800 px-2">Настройки отображения</legend>

        <!-- Максимальная ширина -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div class="md:col-span-2">
            <label class="block mb-1 text-sm font-medium text-gray-700">Максимальная ширина блока:</label>
            <input
              name="displaySettings.maxWidth.value"
              type="number"
              value="100"
              min="1"
              max="100"
              class="w-full border rounded px-3 py-2 focus:ring-2 focus:ring-blue-500"
              placeholder="100"
            />
          </div>
          <div>
            <label class="block mb-1 text-sm font-medium text-gray-700">Единица:</label>
            <select name="displaySettings.maxWidth.unit" class="w-full border rounded px-3 py-2 focus:ring-2 focus:ring-blue-500">
              <option value="%" selected>%</option>
              <option value="px">px</option>
            </select>
          </div>
        </div>

        <!-- Выравнивание -->
        <div class="mb-4">
          <label class="block mb-1 text-sm font-medium text-gray-700">Выравнивание контента:</label>
          <select name="displaySettings.alignment" class="w-full border rounded px-3 py-2 focus:ring-2 focus:ring-blue-500">
            <option value="left" selected>Слева</option>
            <option value="center">По центру</option>
            <option value="right">Справа</option>
          </select>
        </div>

        <!-- Внутренние отступы (padding) -->
        <div class="mb-4">
          <label class="block mb-2 text-sm font-medium text-gray-700">Внутренние отступы (padding):</label>
          <div class="grid grid-cols-2 md:grid-cols-5 gap-2">
            <div>
              <label class="block text-xs text-gray-600 mb-1">Сверху:</label>
              <input
                name="displaySettings.padding.top"
                type="number"
                value="0"
                class="w-full border rounded px-2 py-1 text-sm focus:ring-2 focus:ring-blue-500"
                placeholder="0"
              />
            </div>
            <div>
              <label class="block text-xs text-gray-600 mb-1">Справа:</label>
              <input
                name="displaySettings.padding.right"
                type="number"
                value="0"
                class="w-full border rounded px-2 py-1 text-sm focus:ring-2 focus:ring-blue-500"
                placeholder="0"
              />
            </div>
            <div>
              <label class="block text-xs text-gray-600 mb-1">Снизу:</label>
              <input
                name="displaySettings.padding.bottom"
                type="number"
                value="0"
                class="w-full border rounded px-2 py-1 text-sm focus:ring-2 focus:ring-blue-500"
                placeholder="0"
              />
            </div>
            <div>
              <label class="block text-xs text-gray-600 mb-1">Слева:</label>
              <input
                name="displaySettings.padding.left"
                type="number"
                value="0"
                class="w-full border rounded px-2 py-1 text-sm focus:ring-2 focus:ring-blue-500"
                placeholder="0"
              />
            </div>
            <div>
              <label class="block text-xs text-gray-600 mb-1">Единица:</label>
              <select name="displaySettings.padding.unit" class="w-full border rounded px-2 py-1 text-sm focus:ring-2 focus:ring-blue-500">
                <option value="px" selected>px</option>
                <option value="%">%</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Внешние отступы (margin) -->
        <div class="mb-4">
          <label class="block mb-2 text-sm font-medium text-gray-700">Внешние отступы (margin):</label>
          <div class="grid grid-cols-2 md:grid-cols-5 gap-2">
            <div>
              <label class="block text-xs text-gray-600 mb-1">Сверху:</label>
              <input
                name="displaySettings.margin.top"
                type="number"
                value="0"
                class="w-full border rounded px-2 py-1 text-sm focus:ring-2 focus:ring-blue-500"
                placeholder="0"
              />
            </div>
            <div>
              <label class="block text-xs text-gray-600 mb-1">Справа:</label>
              <input
                name="displaySettings.margin.right"
                type="number"
                value="0"
                class="w-full border rounded px-2 py-1 text-sm focus:ring-2 focus:ring-blue-500"
                placeholder="0"
              />
            </div>
            <div>
              <label class="block text-xs text-gray-600 mb-1">Снизу:</label>
              <input
                name="displaySettings.margin.bottom"
                type="number"
                value="0"
                class="w-full border rounded px-2 py-1 text-sm focus:ring-2 focus:ring-blue-500"
                placeholder="0"
              />
            </div>
            <div>
              <label class="block text-xs text-gray-600 mb-1">Слева:</label>
              <input
                name="displaySettings.margin.left"
                type="number"
                value="0"
                class="w-full border rounded px-2 py-1 text-sm focus:ring-2 focus:ring-blue-500"
                placeholder="0"
              />
            </div>
            <div>
              <label class="block text-xs text-gray-600 mb-1">Единица:</label>
              <select name="displaySettings.margin.unit" class="w-full border rounded px-2 py-1 text-sm focus:ring-2 focus:ring-blue-500">
                <option value="px" selected>px</option>
                <option value="%">%</option>
              </select>
            </div>
          </div>
        </div>


      </fieldset>

      <!-- Новая Grid-система -->
      <fieldset class="border border-gray-200 rounded-lg p-4">
        <legend class="text-base font-semibold text-gray-800 px-2">Grid-система (новая)</legend>
        <p class="text-sm text-gray-600 mb-4">Расширенная система компоновки элементов с поддержкой режимов новичка и профи</p>

        <div id="grid-system-container">
          <CompactGridManager
            blockId={newBlockId}
            settings={{
              enabled: false,
              mode: 'beginner',
              previewActive: false,
              beginnerSettings: {
                columns: 2,
                gap: { value: 1, unit: 'rem' },
                itemAlignment: 'stretch',
                contentAlignment: 'start'
              },
              proSettings: {
                gridTemplateColumns: 'repeat(2, 1fr)',
                gap: { value: 1, unit: 'rem' },
                alignItems: 'stretch',
                justifyItems: 'stretch',
                alignContent: 'start',
                justifyContent: 'start'
              },
              items: []
            }}
          />
        </div>


      </fieldset>

      <div>
        <label class="block mb-1 text-sm font-medium text-gray-700">Контент (Editor.js):</label>
        <div id="editor-container">
          <AdvancedEditorJS id="content" placeholder="Начните создавать контент блока..." minHeight={400} blockType="text" />
        </div>
      </div>
      <div class="flex gap-4 mt-8">
        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-6 py-2 rounded shadow transition">Сохранить</button>
        <a href={`/admin/settings/pages/edit/${id}`} class="bg-gray-200 hover:bg-gray-300 text-gray-700 px-6 py-2 rounded">Отмена</a>
      </div>
    </form>
  </div>
  <script is:inline>
    // Функция для обновления инструментов редактора при изменении типа блока
    function updateEditorTools() {
      const blockTypeSelect = document.getElementById('block-type');
      if (blockTypeSelect) {
        const newBlockType = blockTypeSelect.value;
        console.log(`🔄 Выбран тип блока: ${newBlockType}`);

        // Информация о доступных инструментах для каждого типа
        const toolsInfo = {
          text: ['Заголовки', 'Параграфы', 'Списки', 'Цитаты', 'Код', 'Разделители', 'Таблицы', 'Ссылки', 'HTML'],
          hero: ['Большие заголовки', 'Параграфы', 'Изображения', 'Ссылки', 'HTML'],
          features: ['Заголовки', 'Параграфы', 'Списки', 'Изображения', 'HTML'],
          gallery: ['Заголовки', 'Параграфы', 'Изображения', 'Разделители', 'HTML'],
          contacts: ['Заголовки', 'Параграфы', 'Списки', 'Ссылки', 'HTML'],
          map: ['Заголовки', 'Параграфы', 'Встраивание карт', 'Кнопки', 'HTML'],
          video: ['Заголовки', 'Параграфы', 'Встраивание видео', 'Кнопки', 'HTML'],
          forms: ['Заголовки', 'Параграфы', 'Списки', 'Кнопки', 'HTML'],
          custom: ['Заголовки', 'Параграфы', 'Списки', 'Цитаты', 'Код', 'Таблицы', 'Изображения', 'Ссылки', 'Встраивание', 'Кнопки', 'HTML', 'Разделители']
        };

        const tools = toolsInfo[newBlockType] || toolsInfo.text;
        console.log(`🛠️ Доступные инструменты: ${tools.join(', ')}`);

        // Показываем уведомление пользователю
        const notification = document.createElement('div');
        notification.className = 'fixed top-4 right-4 bg-blue-500 text-white px-4 py-2 rounded shadow-lg z-50';
        notification.textContent = `Выбран тип "${newBlockType}". Доступные инструменты: ${tools.join(', ')}`;
        document.body.appendChild(notification);

        setTimeout(() => {
          notification.remove();
        }, 5000);
      }
    }

    // Валидация настроек отображения
    function validateDisplaySettings() {
      const errors = [];

      // Валидация максимальной ширины
      const maxWidthElement = document.querySelector('input[name="displaySettings.maxWidth.value"]');
      if (maxWidthElement && maxWidthElement.value) {
        const maxWidthValue = parseFloat(maxWidthElement.value);
        if (!isNaN(maxWidthValue) && (maxWidthValue < 1 || maxWidthValue > 100)) {
          errors.push('Максимальная ширина должна быть от 1 до 100');
        }
      }



      // Валидация отступов (проверяем что значения не слишком большие)
      const paddingInputs = ['top', 'right', 'bottom', 'left'];
      paddingInputs.forEach(side => {
        const element = document.querySelector(`input[name="displaySettings.padding.${side}"]`);
        if (element && element.value) {
          const value = parseFloat(element.value);
          if (!isNaN(value) && Math.abs(value) > 1000) {
            errors.push(`Внутренний отступ ${side} не должен превышать 1000px`);
          }
        }
      });

      const marginInputs = ['top', 'right', 'bottom', 'left'];
      marginInputs.forEach(side => {
        const element = document.querySelector(`input[name="displaySettings.margin.${side}"]`);
        if (element && element.value) {
          const value = parseFloat(element.value);
          if (!isNaN(value) && Math.abs(value) > 1000) {
            errors.push(`Внешний отступ ${side} не должен превышать 1000px`);
          }
        }
      });

      return errors;
    }

    // Функция для сбора данных Grid-системы
    function collectGridSystemData() {
      console.log('🔍 collectGridSystemData вызвана (страница создания)');
      console.log('🔍 window.gridSystemManager:', window.gridSystemManager);

      if (window.gridSystemManager) {
        const settings = window.gridSystemManager.getSettings();
        console.log('🔍 Настройки Grid-системы:', settings);

        // Заполняем скрытые поля с проверкой существования элементов
        const gridEnabledField = document.getElementById('grid-enabled-hidden');
        if (gridEnabledField) {
          gridEnabledField.value = settings.enabled ? 'on' : '';
        }

        if (!settings.enabled) {
          return;
        }

        const gridModeField = document.getElementById('grid-mode-hidden');
        if (gridModeField) {
          gridModeField.value = 'simple'; // Упрощенный режим
        }

        // Заполняем поля настроек (упрощенная структура)
        const beginnerColumnsField = document.getElementById('beginner-columns-hidden');
        const beginnerGapValueField = document.getElementById('beginner-gap-value-hidden');
        const beginnerGapUnitField = document.getElementById('beginner-gap-unit-hidden');
        const beginnerItemAlignmentField = document.getElementById('beginner-item-alignment-hidden');
        const beginnerContentAlignmentField = document.getElementById('beginner-content-alignment-hidden');

        if (beginnerColumnsField) beginnerColumnsField.value = settings.columns || 2;
        if (beginnerGapValueField) beginnerGapValueField.value = settings.gap?.value || 1;
        if (beginnerGapUnitField) beginnerGapUnitField.value = settings.gap?.unit || 'rem';
        if (beginnerItemAlignmentField) beginnerItemAlignmentField.value = settings.itemAlignment || 'stretch';
        if (beginnerContentAlignmentField) beginnerContentAlignmentField.value = settings.contentAlignment || 'start';

        // Заполняем данные элементов
        if (settings.items && settings.items.length > 0) {
          document.getElementById('grid-items-data-hidden').value = JSON.stringify(settings.items);
        }
      } else {
        console.warn('window.gridSystemManager не найден');

        // Fallback: собираем данные напрямую из DOM
        // Пробуем разные способы найти чекбокс Grid-системы
        let gridEnabledCheckbox = document.querySelector('input[name="gridEnabled"]');
        if (!gridEnabledCheckbox) {
          // Пробуем найти по ID с blockId
          gridEnabledCheckbox = document.querySelector(`input[id*="grid-enabled"]`);
        }
        if (!gridEnabledCheckbox) {
          // Пробуем найти любой чекбокс в контейнере Grid
          gridEnabledCheckbox = document.querySelector('.grid-toggle input[type="checkbox"]');
        }

        const gridEnabled = gridEnabledCheckbox ? gridEnabledCheckbox.checked : false;

        const gridEnabledField = document.getElementById('grid-enabled-hidden');
        if (gridEnabledField) {
          gridEnabledField.value = gridEnabled ? 'on' : '';
          console.log('🔍 Fallback gridEnabled:', gridEnabledField.value, 'checkbox found:', !!gridEnabledCheckbox);
        }

        if (gridEnabled) {
          let gridModeRadio = document.querySelector('input[name^="gridMode"]:checked');
          if (!gridModeRadio) {
            // Пробуем найти любую выбранную радиокнопку режима
            gridModeRadio = document.querySelector('input[type="radio"]:checked[value*="beginner"], input[type="radio"]:checked[value*="pro"]');
          }

          const mode = gridModeRadio ? gridModeRadio.value : 'beginner';

          const gridModeField = document.getElementById('grid-mode-hidden');
          if (gridModeField) {
            gridModeField.value = mode;
          }

          console.log('🔍 Fallback режим:', mode, 'radio found:', !!gridModeRadio);
        }
      }
    }

    // Обработчик отправки формы
    async function handleFormSubmit() {
      try {
        // Валидируем настройки отображения
        const validationErrors = validateDisplaySettings();
        if (validationErrors.length > 0) {
          alert('Ошибки валидации:\n' + validationErrors.join('\n'));
          return false;
        }

        console.log('📝 Начинаем сохранение данных из EditorJS...');

        // Получаем экземпляр редактора с несколькими попытками
        let editorInstance = window['editor-content_instance'];
        const contentInput = document.getElementById('content-data');
        const editorReady = window['editor-content_ready'];

        console.log('🔍 Проверяем экземпляр редактора:', editorInstance);
        console.log('🔍 Проверяем готовность редактора:', editorReady);
        console.log('🔍 Проверяем поле ввода:', contentInput);

        // Если редактор не найден или не готов, ждем немного и пробуем снова
        if (!editorInstance || !editorReady) {
          console.log('⏳ Редактор не готов, ждем инициализации...');

          // Ждем до 2 секунд для инициализации редактора
          let attempts = 0;
          const maxAttempts = 10;

          while ((!editorInstance || !window['editor-content_ready']) && attempts < maxAttempts) {
            await new Promise(resolve => setTimeout(resolve, 200));
            editorInstance = window['editor-content_instance'];
            attempts++;
            console.log(`⏳ Попытка ${attempts}/${maxAttempts}...`);
          }

          if (!editorInstance) {
            console.warn('⚠️ Редактор не инициализирован после ожидания');
          }
        }

        if (editorInstance && contentInput) {
          console.log('📝 Сохраняем данные из EditorJS...');

          try {
            // Проверяем, что редактор готов
            if (typeof editorInstance.save !== 'function') {
              throw new Error('Редактор не готов к сохранению');
            }

            // Получаем данные из редактора
            const outputData = await editorInstance.save();
            contentInput.value = JSON.stringify(outputData);

            // Собираем данные Grid-системы
            collectGridSystemData();

            console.log('✅ Данные сохранены:', outputData);
            console.log('✅ Значение поля:', contentInput.value);
            return true;
          } catch (saveError) {
            console.error('❌ Ошибка при сохранении данных редактора:', saveError);

            // Fallback: используем последние сохраненные данные
            if (window.editorjsLastData) {
              console.log('🔄 Используем последние сохраненные данные');
              contentInput.value = window.editorjsLastData;
              collectGridSystemData();
              return true;
            }

            throw saveError;
          }
        } else {
          console.error('❌ Редактор или поле не найдены');
          console.log('🔍 Доступные экземпляры редакторов:', Object.keys(window).filter(key => key.includes('editor')));

          // Попробуем использовать глобальную переменную как fallback
          if (contentInput && window.editorjsLastData) {
            console.log('🔄 Используем fallback данные:', window.editorjsLastData);
            contentInput.value = window.editorjsLastData;
            collectGridSystemData();
            return true;
          }

          alert('Ошибка: не удалось получить данные из редактора. Попробуйте еще раз через несколько секунд.');
          return false;
        }
      } catch (error) {
        console.error('❌ Ошибка при сохранении:', error);
        alert('Ошибка при сохранении данных');
        return false;
      }
    }

    // Инициализация Grid-системы
    document.addEventListener('DOMContentLoaded', function() {
      // Загружаем необходимые скрипты для Grid-системы
      const scripts = [
        '/src/settings/utils/editorjs-grid-parser.js'
      ];

      let loadedScripts = 0;

      scripts.forEach(src => {
        const script = document.createElement('script');
        script.src = src;
        script.onload = function() {
          loadedScripts++;
          if (loadedScripts === scripts.length) {
            initializeGridPreview();
          }
        };
        script.onerror = function() {
          console.error(`Ошибка загрузки скрипта: ${src}`);
          loadedScripts++;
          if (loadedScripts === scripts.length) {
            initializeGridPreview();
          }
        };
        document.head.appendChild(script);
      });

      function initializeGridPreview() {
        // CompactGridManager инициализируется автоматически
        // Добавляем функцию рендеринга элементов EditorJS в Grid
        setupEditorJSGridRenderer();
      }

      function setupEditorJSGridRenderer() {
        // Функция для рендеринга элементов EditorJS в Grid
        window.renderEditorJSGridItems = function(items, blockId) {
          if (!window.EditorJSGridParser) {
            console.error('EditorJSGridParser не загружен');
            return;
          }

          const previewContainer = document.querySelector('.grid-preview-content');
          if (!previewContainer) {
            console.error('Контейнер превью Grid не найден');
            return;
          }

          // Очищаем контейнер
          previewContainer.innerHTML = '';

          // Получаем данные из редактора
          if (window.editorInstance) {
            window.editorInstance.save().then(outputData => {
              const blocks = outputData.blocks || [];

              // Преобразуем блоки EditorJS в HTML для каждого элемента Grid
              items.forEach((item, index) => {
                const itemElement = document.createElement('div');
                itemElement.className = 'grid-preview-item';
                itemElement.style.gridArea = item.gridArea || 'auto';

                // Получаем блоки для этого элемента
                const itemBlocks = blocks.slice(index * Math.ceil(blocks.length / items.length), (index + 1) * Math.ceil(blocks.length / items.length));

                if (itemBlocks.length > 0) {
                  const itemContent = itemBlocks.map(block => {
                    switch (block.type) {
                      case 'paragraph':
                        return `<p>${block.data.text || ''}</p>`;
                      case 'header':
                        const level = block.data.level || 2;
                        return `<h${level}>${block.data.text || ''}</h${level}>`;
                      case 'image':
                        return block.data.file ? `<img src="${block.data.file.url}" alt="${block.data.caption || ''}" style="max-width: 100%; height: auto;">` : '';
                      case 'list':
                        const listItems = (block.data.items || []).map(item => `<li>${item}</li>`).join('');
                        return block.data.style === 'ordered' ? `<ol>${listItems}</ol>` : `<ul>${listItems}</ul>`;
                      default:
                        return `<div class="text-gray-500">[${block.type}]</div>`;
                    }
                  }).join('');

                  itemElement.innerHTML = itemContent;
                } else {
                  itemElement.innerHTML = `<div class="text-gray-400 text-center">Элемент ${index + 1}</div>`;
                }

                previewContainer.appendChild(itemElement);
              });

              console.log(`Grid превью обновлено с ${items.length} элементами`);
            }).catch(error => {
              console.error('Ошибка получения данных из редактора:', error);
            });
          } else {
            // Если редактор не готов, показываем заглушки
            items.forEach((item, index) => {
              const itemElement = document.createElement('div');
              itemElement.className = 'grid-preview-item';
              itemElement.style.gridArea = item.gridArea || 'auto';
              itemElement.innerHTML = `<div class="text-gray-400 text-center">Элемент ${index + 1}</div>`;
              previewContainer.appendChild(itemElement);
            });
          }
        };
      }
    });
  </script>
</AdminLayout>
