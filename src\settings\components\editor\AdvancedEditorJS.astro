---
// Продвинутый EditorJS компонент с поддержкой разных типов блоков
export interface Props {
  id?: string;
  placeholder?: string;
  data?: any;
  readonly?: boolean;
  minHeight?: number;
  onChange?: string;
  blockType?: string; // тип блока для определения набора инструментов
}

const {
  id = 'editorjs',
  placeholder = 'Начните писать...',
  data = null,
  readonly = false,
  minHeight = 300,
  onChange = null,
  blockType = 'text'
} = Astro.props;

const editorId = `editor-${id}`;
const hiddenInputId = `${id}-data`;
---

<div class="editorjs-wrapper">
  <div id={editorId} class="editorjs-container"></div>
  <input type="hidden" id={hiddenInputId} name={id} />
</div>

<script is:inline define:vars={{ editorId, hiddenInputId, placeholder, data, readonly, minHeight, onChange, blockType }}>
  window.editorjsConfig = window.editorjsConfig || {};
  window.editorjsConfig[editorId] = {
    editorId,
    hiddenInputId,
    placeholder,
    data,
    readonly,
    minHeight,
    onChange,
    blockType
  };
</script>

<script>
  import EditorJS from '@editorjs/editorjs';
  import Header from '@editorjs/header';
  import List from '@editorjs/list';
  import Paragraph from '@editorjs/paragraph';
  import Quote from '@editorjs/quote';
  import Code from '@editorjs/code';
  import Delimiter from '@editorjs/delimiter';
  import Table from '@editorjs/table';
  import Link from '@editorjs/link';
  import Image from '@editorjs/image';
  import Button from 'editorjs-button';
  import Embed from '@editorjs/embed';
  import Raw from '@editorjs/raw';

  // Плагины успешно импортированы

  // Конфигурации инструментов для разных типов блоков
  const toolConfigs = {
    text: {
      header: {
        class: Header,
        config: {
          placeholder: 'Введите заголовок...',
          levels: [1, 2, 3, 4, 5, 6],
          defaultLevel: 2
        }
      },
      paragraph: {
        class: Paragraph,
        inlineToolbar: true
      },
      list: {
        class: List,
        inlineToolbar: true,
        config: {
          defaultStyle: 'unordered'
        }
      },
      quote: {
        class: Quote,
        inlineToolbar: true,
        config: {
          quotePlaceholder: 'Введите цитату',
          captionPlaceholder: 'Автор цитаты'
        }
      },
      code: {
        class: Code,
        config: {
          placeholder: 'Введите код...'
        }
      },
      delimiter: Delimiter,
      table: {
        class: Table,
        inlineToolbar: true,
        config: {
          rows: 2,
          cols: 3
        }
      },
      linkTool: {
        class: Link,
        config: {
          endpoint: '/api/settings/fetch-url'
        }
      },
      button: {
        class: Button,
        config: {
          placeholder: 'Текст кнопки',
          linkPlaceholder: 'Ссылка'
        }
      },
      raw: {
        class: Raw,
        config: {
          placeholder: 'Введите HTML код...'
        }
      }
    },
    hero: {
      header: {
        class: Header,
        config: {
          placeholder: 'Заголовок героя...',
          levels: [1, 2],
          defaultLevel: 1
        }
      },
      paragraph: {
        class: Paragraph,
        inlineToolbar: true
      },
      image: {
        class: Image,
        config: {
          endpoints: {
            byFile: '/api/settings/editorjs-upload-image',
            byUrl: '/api/settings/editorjs-upload-image'
          }
        }
      },
      linkTool: {
        class: Link,
        config: {
          endpoint: '/api/settings/fetch-url'
        }
      },
      button: {
        class: Button,
        config: {
          placeholder: 'Текст кнопки',
          linkPlaceholder: 'Ссылка'
        }
      },
      raw: {
        class: Raw,
        config: {
          placeholder: 'Введите HTML код...'
        }
      },
      delimiter: Delimiter
    },
    features: {
      header: {
        class: Header,
        config: {
          placeholder: 'Заголовок особенности...',
          levels: [2, 3, 4],
          defaultLevel: 3
        }
      },
      paragraph: {
        class: Paragraph,
        inlineToolbar: true
      },
      list: {
        class: List,
        inlineToolbar: true,
        config: {
          defaultStyle: 'unordered'
        }
      },
      image: {
        class: Image,
        config: {
          endpoints: {
            byFile: '/api/settings/editorjs-upload-image',
            byUrl: '/api/settings/editorjs-upload-image'
          }
        }
      },
      button: {
        class: Button,
        config: {
          placeholder: 'Текст кнопки',
          linkPlaceholder: 'Ссылка'
        }
      },
      raw: {
        class: Raw,
        config: {
          placeholder: 'Введите HTML код...'
        }
      }
    },
    gallery: {
      header: {
        class: Header,
        config: {
          placeholder: 'Заголовок галереи...',
          levels: [2, 3],
          defaultLevel: 2
        }
      },
      paragraph: {
        class: Paragraph,
        inlineToolbar: true
      },
      image: {
        class: Image,
        config: {
          endpoints: {
            byFile: '/api/settings/editorjs-upload-image',
            byUrl: '/api/settings/editorjs-upload-image'
          }
        }
      },
      button: {
        class: Button,
        config: {
          placeholder: 'Текст кнопки',
          linkPlaceholder: 'Ссылка'
        }
      },
      raw: {
        class: Raw,
        config: {
          placeholder: 'Введите HTML код...'
        }
      },
      delimiter: Delimiter
    },

    // Контакты - для контактной информации
    contacts: {
      header: {
        class: Header,
        config: {
          placeholder: 'Заголовок контактов...',
          levels: [2, 3, 4],
          defaultLevel: 2
        }
      },
      paragraph: {
        class: Paragraph,
        inlineToolbar: true
      },
      list: {
        class: List,
        inlineToolbar: true,
        config: {
          defaultStyle: 'unordered'
        }
      },
      linkTool: {
        class: Link,
        config: {
          endpoint: '/api/settings/fetch-url'
        }
      },
      button: {
        class: Button,
        config: {
          placeholder: 'Связаться с нами',
          linkPlaceholder: 'tel:+1234567890'
        }
      },
      raw: {
        class: Raw,
        config: {
          placeholder: 'Введите HTML для поля ввода (например: <input type="text" placeholder="Ваше имя" />)'
        }
      }
    },

    // Карта - для встраивания карт
    map: {
      header: {
        class: Header,
        config: {
          placeholder: 'Заголовок карты...',
          levels: [2, 3, 4],
          defaultLevel: 2
        }
      },
      paragraph: {
        class: Paragraph,
        inlineToolbar: true,
        config: {
          placeholder: 'Введите текст или вставьте ссылку на карту...'
        },
        toolbox: {
          icon: '<svg width="14" height="14" viewBox="0 0 14 14" xmlns="http://www.w3.org/2000/svg"><path d="M1.5 2.5h11a1 1 0 0 1 0 2h-11a1 1 0 0 1 0-2zm0 3h11a1 1 0 0 1 0 2h-11a1 1 0 0 1 0-2zm0 3h11a1 1 0 0 1 0 2h-11a1 1 0 0 1 0-2z"/></svg>',
          title: 'Параграф'
        }
      },
      embed: {
        class: Embed,
        inlineToolbar: false,
        shortcut: 'CMD+SHIFT+E',
        config: {
          services: {
            youtube: true,
            vimeo: true,
            googlemaps: true,
            yandexmaps: {
              regex: /https?:\/\/(www\.)?(yandex\.(ru|com)\/maps|maps\.yandex\.(ru|com))/,
              embedUrl: 'https://yandex.ru/map-widget/v1/?<%= remote_id %>',
              html: '<iframe src="https://yandex.ru/map-widget/v1/?<%= remote_id %>" width="100%" height="300" frameborder="0"></iframe>',
              height: 300,
              width: '100%',
              id: (groups) => {
                // Извлекаем параметры из URL Яндекс.Карт
                const url = groups.join('');
                const urlObj = new URL(url);
                return urlObj.search.substring(1); // убираем ? в начале
              }
            }
          }
        }
      },
      button: {
        class: Button,
        config: {
          placeholder: 'Построить маршрут',
          linkPlaceholder: 'https://maps.google.com'
        }
      },
      raw: {
        class: Raw,
        config: {
          placeholder: 'Введите HTML код...'
        }
      }
    },

    // Видео - для видеоконтента
    video: {
      header: {
        class: Header,
        config: {
          placeholder: 'Заголовок видео...',
          levels: [2, 3, 4],
          defaultLevel: 2
        }
      },
      paragraph: {
        class: Paragraph,
        inlineToolbar: true,
        config: {
          placeholder: 'Введите текст или вставьте ссылку для встраивания...'
        },
        toolbox: {
          icon: '<svg width="14" height="14" viewBox="0 0 14 14" xmlns="http://www.w3.org/2000/svg"><path d="M1.5 2.5h11a1 1 0 0 1 0 2h-11a1 1 0 0 1 0-2zm0 3h11a1 1 0 0 1 0 2h-11a1 1 0 0 1 0-2zm0 3h11a1 1 0 0 1 0 2h-11a1 1 0 0 1 0-2z"/></svg>',
          title: 'Параграф'
        }
      },
      embed: {
        class: Embed,
        inlineToolbar: false,
        shortcut: 'CMD+SHIFT+E',
        config: {
          services: {
            youtube: true,
            vimeo: true,
            coub: true,
            facebook: true,
            instagram: true,
            twitter: true,
            twitch: true
          }
        }
      },
      button: {
        class: Button,
        config: {
          placeholder: 'Смотреть больше',
          linkPlaceholder: 'https://youtube.com'
        }
      },
      raw: {
        class: Raw,
        config: {
          placeholder: 'Введите HTML код...'
        }
      },
      delimiter: Delimiter
    },

    // Формы - для форм обратной связи
    forms: {
      header: {
        class: Header,
        config: {
          placeholder: 'Заголовок формы...',
          levels: [2, 3, 4],
          defaultLevel: 2
        }
      },
      paragraph: {
        class: Paragraph,
        inlineToolbar: true
      },
      list: {
        class: List,
        inlineToolbar: true,
        config: {
          defaultStyle: 'unordered'
        }
      },
      button: {
        class: Button,
        config: {
          placeholder: 'Отправить',
          linkPlaceholder: '#submit-form'
        }
      },
      raw: {
        class: Raw,
        config: {
          placeholder: 'Введите HTML для полей формы (например: <input type="email" placeholder="Email" required />)'
        }
      },
      delimiter: Delimiter
    },

    // Custom - универсальный тип со всеми инструментами
    custom: {
      header: {
        class: Header,
        config: {
          placeholder: 'Введите заголовок...',
          levels: [1, 2, 3, 4, 5, 6],
          defaultLevel: 2
        }
      },
      paragraph: {
        class: Paragraph,
        inlineToolbar: true,
        config: {
          placeholder: 'Введите текст...'
        }
      },
      list: {
        class: List,
        inlineToolbar: true,
        config: {
          defaultStyle: 'unordered'
        }
      },
      quote: {
        class: Quote,
        inlineToolbar: true,
        config: {
          quotePlaceholder: 'Введите цитату',
          captionPlaceholder: 'Автор цитаты'
        }
      },
      code: {
        class: Code,
        config: {
          placeholder: 'Введите код...'
        }
      },
      table: {
        class: Table,
        inlineToolbar: true,
        config: {
          rows: 2,
          cols: 3
        }
      },
      image: {
        class: Image,
        config: {
          endpoints: {
            byFile: '/api/settings/editorjs-upload-image',
            byUrl: '/api/settings/editorjs-upload-image'
          }
        }
      },
      linkTool: {
        class: Link,
        config: {
          endpoint: '/api/settings/fetch-url'
        }
      },
      embed: {
        class: Embed,
        inlineToolbar: false,
        shortcut: 'CMD+SHIFT+E',
        config: {
          services: {
            youtube: true,
            vimeo: true,
            coub: true,
            facebook: true,
            instagram: true,
            twitter: true,
            twitch: true,
            googlemaps: true,
            yandexmaps: {
              regex: /https?:\/\/(www\.)?(yandex\.(ru|com)\/map-widget|maps\.yandex\.(ru|com))/,
              embedUrl: '<%= remote_id %>',
              html: '<iframe src="<%= remote_id %>" width="100%" height="300" frameborder="0"></iframe>',
              height: 300,
              width: '100%'
            }
          }
        }
      },
      button: {
        class: Button,
        config: {
          placeholder: 'Текст кнопки',
          linkPlaceholder: 'Ссылка'
        }
      },
      raw: {
        class: Raw,
        config: {
          placeholder: 'Введите HTML код...'
        }
      },
      delimiter: Delimiter
    }
  };

  function initEditor() {
    const configs = window.editorjsConfig;
    if (!configs) {
      setTimeout(initEditor, 10);
      return;
    }

    Object.values(configs).forEach(config => {
      const {
        editorId,
        hiddenInputId,
        placeholder,
        data,
        readonly,
        minHeight,
        onChange,
        blockType
      } = config;

      function createEditor() {
        try {
          const container = document.getElementById(editorId);
          if (!container) {
            return;
          }

          // Получаем инструменты для типа блока
          const tools = toolConfigs[blockType] || toolConfigs.text;

          const editor = new EditorJS({
            holder: editorId,
            tools: tools,
            data: data || {},
            readOnly: readonly,
            placeholder: placeholder,
            minHeight: minHeight,
            onChange: async (api, event) => {
              try {
                const outputData = await editor.save();
                const hiddenInput = document.getElementById(hiddenInputId);

                if (hiddenInput) {
                  hiddenInput.value = JSON.stringify(outputData);
                }

                window.editorjsLastData = JSON.stringify(outputData);

                if (onChange && typeof window[onChange] === 'function') {
                  window[onChange](outputData, api, event);
                }

              } catch (error) {
                // Ошибка при сохранении данных
              }
            },
            onReady: () => {
              // Устанавливаем минимальную высоту
              const editorContainer = document.querySelector('#' + editorId + ' .codex-editor');
              if (editorContainer) {
                editorContainer.style.minHeight = minHeight + 'px';
              }

              // Диагностика тулбокса
              setTimeout(() => {
                const toolbox = document.querySelector('#' + editorId + ' .ce-toolbox');
                if (toolbox) {
                  const toolButtons = toolbox.querySelectorAll('.ce-toolbox__button');
                  // Инструменты загружены
                }
              }, 1000);
            }
          });

          // Сохраняем экземпляр
          window[editorId + '_instance'] = editor;

          // Добавляем флаг готовности
          window[editorId + '_ready'] = true;

          // Функция для обновления инструментов
          window.updateEditorTools = function() {
            const blockTypeSelect = document.getElementById('block-type');
            if (blockTypeSelect) {
              const newBlockType = blockTypeSelect.value;

              // Получаем инструменты для нового типа
              const newTools = toolConfigs[newBlockType] || toolConfigs.text;
              const toolsInfo = Object.keys(newTools);

              // Показываем уведомление пользователю
              showToolsNotification(newBlockType, toolsInfo);

              // Пересоздаем редактор с новыми инструментами
              recreateEditor(newBlockType, newTools);
            }
          };

          // Функция для показа уведомления
          function showToolsNotification(blockType, tools) {
            // Удаляем предыдущее уведомление если есть
            const existingNotification = document.querySelector('.tools-notification');
            if (existingNotification) {
              existingNotification.remove();
            }

            const notification = document.createElement('div');
            notification.className = 'tools-notification fixed top-4 right-4 bg-blue-500 text-white px-4 py-3 rounded-lg shadow-lg z-50 max-w-sm';

            const titleDiv = document.createElement('div');
            titleDiv.className = 'font-semibold mb-1';
            titleDiv.textContent = 'Тип блока: ' + blockType;

            const labelDiv = document.createElement('div');
            labelDiv.className = 'text-sm';
            labelDiv.textContent = 'Доступные инструменты:';

            const toolsDiv = document.createElement('div');
            toolsDiv.className = 'text-sm mt-1';
            toolsDiv.textContent = tools.join(', ');

            notification.appendChild(titleDiv);
            notification.appendChild(labelDiv);
            notification.appendChild(toolsDiv);

            document.body.appendChild(notification);

            setTimeout(() => {
              notification.remove();
            }, 5000);
          }

          // Функция для пересоздания редактора
          function recreateEditor(blockType, tools) {
            try {
              // Сбрасываем флаг готовности
              window[editorId + '_ready'] = false;

              // Уничтожаем старый редактор
              const oldEditor = window[editorId + '_instance'];
              if (oldEditor && typeof oldEditor.destroy === 'function') {
                oldEditor.destroy();
              }

              // Очищаем контейнер
              const container = document.getElementById(editorId);
              if (container) {
                container.innerHTML = '';
              }

              // Создаем новый редактор с новыми инструментами
              setTimeout(() => {
                const newEditor = new EditorJS({
                  holder: editorId,
                  tools: tools,
                  data: {},
                  readOnly: readonly,
                  placeholder: placeholder,
                  minHeight: minHeight,
                  onChange: async (api, event) => {
                    try {
                      const outputData = await newEditor.save();
                      const hiddenInput = document.getElementById(hiddenInputId);

                      if (hiddenInput) {
                        hiddenInput.value = JSON.stringify(outputData);
                      }

                      window.editorjsLastData = JSON.stringify(outputData);

                      if (onChange && typeof window[onChange] === 'function') {
                        window[onChange](outputData, api, event);
                      }

                    } catch (error) {
                      // Ошибка при сохранении данных
                    }
                  },
                  onReady: () => {

                    const editorContainer = document.querySelector('#' + editorId + ' .codex-editor');
                    if (editorContainer) {
                      editorContainer.style.minHeight = minHeight + 'px';
                    }
                  }
                });

                // Сохраняем новый экземпляр
                window[editorId + '_instance'] = newEditor;
                window[editorId + '_ready'] = true;
              }, 100);

            } catch (error) {
              // Ошибка при пересоздании редактора
            }
          }

        } catch (error) {
          showError(editorId);
        }
      }

      function showError(editorId) {
        const container = document.getElementById(editorId);
        if (container) {
          container.innerHTML =
            '<div style="padding: 20px; border: 2px dashed #ef4444; border-radius: 8px; text-align: center; color: #ef4444;">' +
              '<p><strong>Ошибка загрузки редактора</strong></p>' +
              '<p>Не удалось создать EditorJS. Проверьте консоль для деталей.</p>' +
              '<button onclick="location.reload()" style="margin-top: 10px; padding: 8px 16px; background: #ef4444; color: white; border: none; border-radius: 4px; cursor: pointer;">' +
                'Перезагрузить страницу' +
              '</button>' +
            '</div>';
        }
      }

      setTimeout(createEditor, 100);
    });
  }

  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initEditor);
  } else {
    initEditor();
  }
</script>

<style>
  .editorjs-wrapper {
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    background: white;
    overflow: hidden;
  }

  .editorjs-container {
    min-height: 300px;
    padding: 1rem;
  }

  /* Стили для EditorJS */
  :global(.codex-editor) {
    font-family: inherit;
  }

  :global(.codex-editor__redactor) {
    padding-bottom: 1rem !important;
  }

  :global(.ce-block__content) {
    max-width: none !important;
  }

  :global(.ce-toolbar__content) {
    max-width: none !important;
  }

  :global(.ce-block) {
    margin: 0.5rem 0;
  }

  :global(.ce-paragraph) {
    line-height: 1.6;
  }

  :global(.ce-header) {
    margin: 1rem 0 0.5rem 0;
  }

  :global(.ce-quote) {
    border-left: 4px solid #3b82f6;
    padding-left: 1rem;
    margin: 1rem 0;
    font-style: italic;
  }

  :global(.ce-code) {
    background: #f3f4f6;
    border-radius: 0.375rem;
    padding: 1rem;
    font-family: 'Courier New', monospace;
    margin: 1rem 0;
  }

  :global(.ce-delimiter) {
    margin: 2rem 0;
    text-align: center;
  }

  :global(.ce-table) {
    margin: 1rem 0;
  }

  :global(.ce-table table) {
    width: 100%;
    border-collapse: collapse;
  }

  :global(.ce-table td) {
    border: 1px solid #e5e7eb;
    padding: 0.5rem;
  }

  :global(.ce-inline-toolbar) {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  :global(.codex-editor__redactor) {
    padding-left: 6%;
  }

  :global(.ce-toolbar) {
    left: 6% !important;
  }
</style>
